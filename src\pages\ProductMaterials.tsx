import { useState, useEffect } from 'react';
import Layout from '@/components/layout/Layout';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from '@/components/ui/button';
import { Search, Plus, MoreVertical, Loader2, Save, X, Eye, Edit, Trash2 } from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from 'sonner';
import { useRef } from 'react';

const UNITES = ['kg', 'L', 'pcs', 'm', 'g'];

// Mock categories (fournisseurs)
const MOCK_CATEGORIES = [
  { id: '1', name: 'Fournisseur A' },
  { id: '2', name: 'Fournisseur B' },
  { id: '3', name: 'Fournisseur C' },
];

function getRandomInt(min: number, max: number) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// Add this helper for mock materials
function generateMockMaterials() {
  return [
    { name: 'Matière A', quantity: 10, unit: 'kg' },
    { name: 'Matière B', quantity: 5, unit: 'L' },
    { name: 'Matière C', quantity: 2, unit: 'pcs' },
  ];
}

// Add type to product (semi or finis)
function generateRandomProduct(index: number) {
  const ref = `REF-${1000 + index}`;
  const nom = `Produit ${String.fromCharCode(65 + (index % 26))}`;
  const type = index % 2 === 0 ? 'semi' : 'finis';
  return {
    reference: ref,
    nom,
    type,
    materials: generateMockMaterials(),
  };
}

const INITIAL_PRODUCTS = Array.from({ length: 10 }, (_, i) => generateRandomProduct(i));

const ProductMaterials = () => {
  const [products, setProducts] = useState<any[]>([]); // Now empty, will be filled from API
  const [searchTerm, setSearchTerm] = useState('');
  const [openMenuIndex, setOpenMenuIndex] = useState<number | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showMaterialsModal, setShowMaterialsModal] = useState(false);
  const [selectedProductIdx, setSelectedProductIdx] = useState<number | null>(null);
  const [typeFilter, setTypeFilter] = useState<'all' | 'semi' | 'finis'>('all');
  const [menuPosition, setMenuPosition] = useState<{ top: number; left: number } | null>(null);

  // Add Product form state
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState(MOCK_CATEGORIES);
  const [categoriesLoading] = useState(false);
  const [product, setProduct] = useState({
    reference: '',
    nom: '',
    type: 'semi',
    materials: [] as { name: string; quantity: number; unit: string; materialType: string }[],
  });
  // Add state for duplicate reference check
  const [isDuplicateReference, setIsDuplicateReference] = useState(false);
  // Add state for backend duplicate error
  const [backendDuplicateReference, setBackendDuplicateReference] = useState(false);

  // Check for duplicate reference when product.reference changes
  useEffect(() => {
    setIsDuplicateReference(
      product.reference.trim() !== '' &&
      products.some(p => p.reference.trim().toLowerCase() === product.reference.trim().toLowerCase())
    );
    // Clear backend duplicate error if user changes reference
    setBackendDuplicateReference(false);
  }, [product.reference, products]);
  // For new material modal in add modal
  const [showAddMaterialModal, setShowAddMaterialModal] = useState(false);
  const [newMatName, setNewMatName] = useState('');
  const [newMatQty, setNewMatQty] = useState('');
  const [newMatUnit, setNewMatUnit] = useState(UNITES[0]);
  const [newMatType, setNewMatType] = useState<'matiere' | 'semi'>('matiere');
  // Separate modals for finished products
  const [showAddRawMaterialModal, setShowAddRawMaterialModal] = useState(false);
  const [showAddSemiProductModal, setShowAddSemiProductModal] = useState(false);
  
  // Raw material form state
  const [rawMatName, setRawMatName] = useState('');
  const [rawMatQty, setRawMatQty] = useState('');
  const [rawMatUnit, setRawMatUnit] = useState(UNITES[0]);
  // Semi product form state
  const [semiProdName, setSemiProdName] = useState('');
  const [semiProdQty, setSemiProdQty] = useState('');
  const [semiProdUnit, setSemiProdUnit] = useState(UNITES[0]);

  // Edit form state
  const [editProduct, setEditProduct] = useState({
    reference: '',
    nom: '',
    type: 'semi',
    materials: [] as { name: string; quantity: number; unit: string; materialType: string }[],
  });

  // Material management state
  const [materialName, setMaterialName] = useState('');
  const [materialQty, setMaterialQty] = useState('');
  const [materialUnit, setMaterialUnit] = useState(UNITES[0]);

  // Edit modal state for material
  const [showEditMaterialModal, setShowEditMaterialModal] = useState(false);
  const [editMatName, setEditMatName] = useState('');
  const [editMatQty, setEditMatQty] = useState('');
  const [editMatUnit, setEditMatUnit] = useState(UNITES[0]);
  const [editMatType, setEditMatType] = useState<'matiere' | 'semi'>('matiere');
  // Edit modal: add raw material and semi-fini modals
  const [showEditAddRawMaterialModal, setShowEditAddRawMaterialModal] = useState(false);
  const [editRawMatName, setEditRawMatName] = useState('');
  const [editRawMatQty, setEditRawMatQty] = useState('');
  const [editRawMatUnit, setEditRawMatUnit] = useState(UNITES[0]);
  const [showEditAddSemiProductModal, setShowEditAddSemiProductModal] = useState(false);
  const [editSemiProdName, setEditSemiProdName] = useState('');
  const [editSemiProdQty, setEditSemiProdQty] = useState('');
  const [editSemiProdUnit, setEditSemiProdUnit] = useState(UNITES[0]);

  // For edit modal, track which product is being edited
  const editModalRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    if (!showEditModal) return;
    const modal = editModalRef.current;
    if (!modal) return;
    let timeout: any;
    const handleScroll = () => {
      modal.classList.add('scrolling');
      clearTimeout(timeout);
      timeout = setTimeout(() => {
        modal.classList.remove('scrolling');
      }, 700);
    };
    modal.addEventListener('scroll', handleScroll);
    modal.classList.remove('scrolling');
    return () => {
      modal.removeEventListener('scroll', handleScroll);
      clearTimeout(timeout);
    };
  }, [showEditModal]);

  // Add material in edit modal
  const handleAddMaterialEdit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!editMatName || !editMatQty || isNaN(Number(editMatQty))) return;
    setEditProduct({
      ...editProduct,
      materials: [
        ...(editProduct.materials || []),
        { name: editMatName, quantity: Number(editMatQty), unit: editMatUnit, materialType: editMatType }
      ]
    });
    setEditMatName('');
    setEditMatQty('');
    setEditMatUnit(UNITES[0]);
    setEditMatType('matiere');
    setShowEditMaterialModal(false);
  };
  // Remove material in edit modal
  const handleRemoveMaterialEdit = (idx: number) => {
    setEditProduct({
      ...editProduct,
      materials: (editProduct.materials || []).filter((_, i) => i !== idx)
    });
  };

  const filteredProducts = products.filter(product =>
    (typeFilter === 'all' || product.type === typeFilter) &&
    (product.reference.toLowerCase().includes(searchTerm.toLowerCase()) ||
     product.nom.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleAddProduct = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    if (!product.reference || !product.nom || !product.type) {
      toast.error('Please fill in all required fields');
      setLoading(false);
      return;
    }
    if (isDuplicateReference) {
      setLoading(false);
      return;
    }
    try {
      const res = await fetch('http://localhost:3001/api/products-with-materials', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          reference: product.reference,
          nom: product.nom,
          type: product.type,
          materials: product.materials,
        }),
      });
      if (!res.ok) {
        let isDup = false;
        try {
          const errorData = await res.json();
          if (
            errorData &&
            (errorData.error === 'Database error' || errorData.message === 'Database error') &&
            errorData.details &&
            (errorData.details.code === 'ER_DUP_ENTRY' || errorData.details.errno === 1062)
          ) {
            isDup = true;
          }
        } catch {}
        if (isDup) {
          setBackendDuplicateReference(true);
        } else {
          toast.error('Erreur lors de la création du produit');
        }
        setLoading(false);
        return;
      }
      const newProduct = await res.json();
      setProducts([...products, newProduct]);
      setProduct({ reference: '', nom: '', type: 'semi', materials: [] });
      setShowAddModal(false);
      toast.success('Produit ajouté avec succès!');
    } catch (err) {
      toast.error('Erreur lors de la création du produit');
    }
    setLoading(false);
  };

  // Add material row in add modal
  const handleAddMaterialRow = (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    if (!newMatName || !newMatQty || isNaN(Number(newMatQty))) return;
    setProduct({
      ...product,
      materials: [
        ...product.materials,
        { name: newMatName, quantity: Number(newMatQty), unit: newMatUnit, materialType: newMatType }
      ]
    });
    setNewMatName('');
    setNewMatQty('');
    setNewMatUnit(UNITES[0]);
    setNewMatType('matiere');
  };

  // Remove material row in add modal
  const handleRemoveMaterialRow = (idx: number) => {
    setProduct({
      ...product,
      materials: product.materials.filter((_, i) => i !== idx)
    });
  };

  // Add raw material handler
  const handleAddRawMaterial = (e: React.FormEvent) => {
    e.preventDefault();
    if (!rawMatName || !rawMatQty || isNaN(Number(rawMatQty))) return;
    setProduct({
      ...product,
      materials: [
        ...product.materials,
        { name: rawMatName, quantity: Number(rawMatQty), unit: rawMatUnit, materialType: 'matiere' }
      ]
    });
    setRawMatName('');
    setRawMatQty('');
    setRawMatUnit(UNITES[0]);
    setShowAddRawMaterialModal(false);
  };

  // Add semi product handler
  const handleAddSemiProduct = (e: React.FormEvent) => {
    e.preventDefault();
    if (!semiProdName || !semiProdQty || isNaN(Number(semiProdQty))) return;
    setProduct({
      ...product,
      materials: [
        ...product.materials,
        { name: semiProdName, quantity: Number(semiProdQty), unit: semiProdUnit, materialType: 'semi' }
      ]
    });
    setSemiProdName('');
    setSemiProdQty('');
    setSemiProdUnit(UNITES[0]);
    setShowAddSemiProductModal(false);
  };

  // Open Edit Modal
  const handleEdit = async (idx: number) => {
    setSelectedProductIdx(idx);
    const product = products[idx];
    let materials = [];
    try {
      const res = await fetch(`http://localhost:3001/api/products/${product.id}/materials`);
      if (res.ok) {
        const rawMaterials = await res.json();
        materials = (rawMaterials || []).map((mat: any) => ({
          ...mat,
          materialType: mat.material_type || mat.materialType || 'matiere',
        }));
      }
    } catch (e) {
      materials = [];
    }
    setEditProduct({
      reference: product.reference,
      nom: product.nom,
      type: product.type,
      materials: materials || [],
    });
    setShowEditModal(true);
    setOpenMenuIndex(null);
  };

  // Save Edit
  const handleEditSave = async (e: React.FormEvent) => {
    e.preventDefault();
    if (selectedProductIdx === null) return;
    setLoading(true);
    try {
      const productId = products[selectedProductIdx].id;
      const res = await fetch(`http://localhost:3001/api/products/${productId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(editProduct),
      });
      if (!res.ok) throw new Error('Erreur lors de la modification');
      const updatedProduct = await res.json();
      const updated = [...products];
      updated[selectedProductIdx] = updatedProduct;
      setProducts(updated);
      setShowEditModal(false);
      toast.success('Produit modifié !');
    } catch (err) {
      toast.error('Erreur lors de la modification du produit');
    }
    setLoading(false);
  };

  // Add state for materials in details popup
  const [showMaterialsList, setShowMaterialsList] = useState<any[]>([]);
  const [allMaterialsList, setAllMaterialsList] = useState<any[]>([]);

  // Open Show Materials Modal
  const handleShowMaterials = async (idx: number) => {
    setSelectedProductIdx(idx);
    const product = products[idx];
    let materials = [];
    try {
      const res = await fetch(`http://localhost:3001/api/products/${product.id}/materials`);
      if (res.ok) {
        const rawMaterials = await res.json();
        materials = (rawMaterials || []).map((mat: any) => ({
          ...mat,
          materialType: mat.material_type || mat.materialType || 'matiere',
        }));
      }
    } catch (e) {
      materials = [];
    }
    setAllMaterialsList(materials || []);
    setShowMaterialsList(materials || []);
    setShowMaterialsModal(true);
    setOpenMenuIndex(null);
  };

  // Add Material to Product
  const handleAddMaterialToProduct = (e: React.FormEvent) => {
    e.preventDefault();
    if (!materialName || !materialQty || isNaN(Number(materialQty))) return;
    if (selectedProductIdx === null) return;
    const updated = [...products];
    updated[selectedProductIdx] = {
      ...updated[selectedProductIdx],
      materials: [
        ...updated[selectedProductIdx].materials,
        { name: materialName, quantity: Number(materialQty), unit: materialUnit }
      ]
    };
    setProducts(updated);
    setMaterialName('');
    setMaterialQty('');
    setMaterialUnit(UNITES[0]);
  };

  // Remove Material from Product
  const handleRemoveMaterial = (matIdx: number) => {
    if (selectedProductIdx === null) return;
    const updated = [...products];
    updated[selectedProductIdx] = {
      ...updated[selectedProductIdx],
      materials: updated[selectedProductIdx].materials.filter((_, i) => i !== matIdx)
    };
    setProducts(updated);
  };

  // Remove MATERIAL_OPTIONS and add state for materials
  // const MATERIAL_OPTIONS = [...];
  // Change materialOptions to store both nom and unite
  const [materialOptions, setMaterialOptions] = useState<{ nom: string; unite: string; type: string }[]>([]);

  // Fetch material options (matière première and semi-finished products) from backend
  useEffect(() => {
    const fetchMaterials = async () => {
      try {
        const res = await fetch('http://localhost:3001/api/available-materials');
        const data = await res.json();
        setMaterialOptions(data.map((m: any) => ({ nom: m.nom, unite: m.unite, type: m.type })));
      } catch (error) {
        setMaterialOptions([]);
      }
    };
    fetchMaterials();
  }, []);

  // Helper to get unite for selected material
  const getUniteForMaterial = (name: string, type: string) => {
    const found = materialOptions.find(m => m.nom === name && m.type === type);
    return found ? found.unite : UNITES[0];
  };

  const addModalRef = useRef<HTMLDivElement>(null);

  // Scrollbar visibility effect for add modal
  useEffect(() => {
    if (!showAddModal) return;
    const modal = addModalRef.current;
    if (!modal) return;
    let timeout: any;
    const handleScroll = () => {
      modal.classList.add('scrolling');
      clearTimeout(timeout);
      timeout = setTimeout(() => {
        modal.classList.remove('scrolling');
      }, 700);
    };
    modal.addEventListener('scroll', handleScroll);
    // Initially hide
    modal.classList.remove('scrolling');
    return () => {
      modal.removeEventListener('scroll', handleScroll);
      clearTimeout(timeout);
    };
  }, [showAddModal]);

  // Fetch products from backend on mount
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const res = await fetch('http://localhost:3001/api/products');
        const data = await res.json();
        // Only keep products that are not type 'matiere'
        const filtered = data.filter((p: any) => p.type !== 'matiere');
        setProducts(filtered);
      } catch (error) {
        setProducts([]);
      }
    };
    fetchProducts();
  }, []);

  return (
    <Layout>
      <div className="mb-4 sm:mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold mb-1">BOM Produits</h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Liste des produits et gestion des nomenclatures matières premières
            </p>
          </div>
          <Button onClick={() => setShowAddModal(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Ajouter Produit
          </Button>
        </div>
      </div>

      {/* Filter and search bar */}
      <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 mb-6 items-center">
        <div className="relative flex-1 w-full">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Rechercher un produit..."
            className="pl-10 text-sm sm:text-base"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="w-full sm:w-48">
          <Select value={typeFilter} onValueChange={v => setTypeFilter(v as 'all' | 'semi' | 'finis')}>
            <SelectTrigger>
              <SelectValue placeholder="Filtrer par type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tous les types</SelectItem>
              <SelectItem value="semi">Semi-fini</SelectItem>
              <SelectItem value="finis">Fini</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Card>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Reference</TableHead>
                <TableHead>Nom</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredProducts.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={4} className="text-center py-8">
                    Aucun résultat trouvé.
                  </TableCell>
                </TableRow>
              ) : (
                filteredProducts.map((product, idx) => (
                  <TableRow key={product.reference + idx}>
                    <TableCell>{product.reference}</TableCell>
                    <TableCell>{product.nom}</TableCell>
                    <TableCell>{product.type === 'semi' ? 'Semi-fini' : 'Fini'}</TableCell>
                    <TableCell>
                      <button
                        className="p-2 rounded-full hover:bg-gray-100"
                        onClick={() => handleShowMaterials(idx)}
                        aria-label="Show Materials"
                      >
                        <Eye size={18} />
                      </button>
                      {/* Remove relative from this div */}
                      <div className="inline-block ml-1">
                        <button
                          className="p-2 rounded-full hover:bg-gray-100"
                          onClick={e => {
                            e.stopPropagation();
                            if (openMenuIndex === idx) {
                              setOpenMenuIndex(null);
                              setMenuPosition(null);
                            } else {
                              setOpenMenuIndex(idx);
                              const rect = e.currentTarget.getBoundingClientRect();
                              setMenuPosition({ top: rect.bottom + window.scrollY, left: rect.right + window.scrollX - 150 }); // 150 = menu width
                            }
                          }}
                          aria-label="Actions"
                        >
                          <MoreVertical size={18} />
                        </button>
                        {/* Render menu as fixed-positioned element */}
                        {openMenuIndex === idx && menuPosition && (
                          <div
                            className="z-50 w-36 bg-white border border-gray-200 rounded shadow-lg"
                            style={{
                              position: 'fixed',
                              top: menuPosition.top,
                              left: menuPosition.left,
                            }}
                          >
                            <button
                              className="flex items-center w-full text-left px-4 py-2 hover:bg-gray-100"
                              onClick={() => handleEdit(idx)}
                            >
                              <Edit className="mr-2 h-4 w-4" /> Edit
                            </button>
                            <button
                              className="flex items-center w-full text-left px-4 py-2 hover:bg-gray-100 text-red-600"
                              onClick={async () => {
                                if (!window.confirm('Are you sure you want to delete this product?')) return;
                                const productId = products[idx].id;
                                try {
                                  const res = await fetch(`http://localhost:3001/api/products/${productId}`, { method: 'DELETE' });
                                  if (!res.ok) throw new Error('Failed to delete');
                                  setProducts(products => products.filter((_, i) => i !== idx));
                                  setOpenMenuIndex(null);
                                  setMenuPosition(null);
                                } catch (err) {
                                  alert('Failed to delete product');
                                }
                              }}
                            >
                              <Trash2 className="mr-2 h-4 w-4" /> Delete
                            </button>
                          </div>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </Card>

      {/* Add Product Modal */}
      {showAddModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30">
          <div ref={addModalRef} className="bg-white rounded-lg shadow-lg w-full max-w-lg relative max-h-[90vh] flex flex-col custom-scrollbar">
            {/* Sticky Header */}
            <div className="sticky top-0 z-10 bg-white p-6 pb-2 flex items-center justify-between border-b">
              <h2 className="text-xl font-bold">Ajouter un produit</h2>
              <button
                className="text-gray-500 hover:text-black ml-4"
                onClick={() => setShowAddModal(false)}
              >
                <X size={20} />
              </button>
            </div>
            {/* Scrollable Content */}
            <div className="overflow-y-auto px-6 pt-2 pb-0 flex-1 min-h-0">
              <form id="add-product-form" onSubmit={handleAddProduct} className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Informations Produit</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="reference">Référence *</Label>
                      <Input
                        id="reference"
                        value={product.reference}
                        onChange={(e) => setProduct({ ...product, reference: e.target.value })}
                        placeholder="Entrer la référence"
                        required
                        className={isDuplicateReference || backendDuplicateReference ? 'border-red-500 focus-visible:ring-red-500' : ''}
                      />
                      {(isDuplicateReference || backendDuplicateReference) && (
                        <div className="text-red-500 text-xs mt-1">Cette référence existe déjà.</div>
                      )}
                    </div>
                    <div>
                      <Label htmlFor="nom">Nom *</Label>
                      <Input
                        id="nom"
                        value={product.nom}
                        onChange={(e) => setProduct({ ...product, nom: e.target.value })}
                        placeholder="Entrer le nom du produit"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="type">Type *</Label>
                      <Select
                        value={product.type}
                        onValueChange={(value) => setProduct({ ...product, type: value })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionner le type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="semi">Semi-fini</SelectItem>
                          <SelectItem value="finis">Fini</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label className="mb-2">Structure des matières premières</Label>
                      <div className="space-y-2">
                        {product.materials.length === 0 ? (
                          <div className="text-muted-foreground text-sm">Aucune matière ajoutée.</div>
                        ) : (
                          <div className="mb-2 max-h-40 overflow-y-auto border rounded bg-muted/10">
                            <ul>
                              {product.materials.map((mat, idx) => (
                                <li key={idx} className="flex items-center gap-2 border-b py-1 px-2 last:border-b-0">
                                  <span>{mat.name} - {mat.quantity} {mat.unit} ({mat.materialType === 'matiere' ? 'Matière première' : 'Semi-fini'})</span>
                                  <button type="button" className="text-red-500 hover:underline text-xs" onClick={() => handleRemoveMaterialRow(idx)}>Supprimer</button>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                        {(product.type === 'finis' || product.type === 'semi') ? (
                          <div className="flex gap-2">
                            <Button type="button" onClick={() => setShowAddRawMaterialModal(true)}>
                              Ajouter Matière Première
                            </Button>
                            <Button type="button" onClick={() => setShowAddSemiProductModal(true)}>
                              Ajouter Semi-fini
                            </Button>
                          </div>
                        ) : (
                          <Button type="button" onClick={() => setShowAddMaterialModal(true)}>Ajouter</Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </form>
            </div>
            {/* Sticky Footer */}
            <div className="sticky bottom-0 z-10 bg-white p-6 pt-2 border-t flex justify-end gap-2">
              <Button type="button" variant="outline" onClick={() => setShowAddModal(false)}>
                Annuler
              </Button>
              <Button type="submit" form="add-product-form" disabled={loading}>
                {loading ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Création...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Ajouter
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Add Material Modal (for Add Product) */}
      {showAddMaterialModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-6 relative">
            <button
              className="absolute top-3 right-3 text-gray-500 hover:text-black"
              onClick={() => setShowAddMaterialModal(false)}
            >
              <X size={20} />
            </button>
            <h2 className="text-xl font-bold mb-4">Ajouter une matière première</h2>
            <form onSubmit={e => {
              e.preventDefault();
              if (!newMatName || !newMatQty || isNaN(Number(newMatQty))) return;
              setProduct({
                ...product,
                materials: [
                  ...product.materials,
                  { name: newMatName, quantity: Number(newMatQty), unit: newMatUnit, materialType: newMatType }
                ]
              });
              setNewMatName('');
              setNewMatQty('');
              setNewMatUnit(UNITES[0]);
              setShowAddMaterialModal(false);
            }} className="space-y-4">
              <div>
                <Label>Nom *</Label>
                <Select
                  value={materialOptions.some(opt => opt.nom && opt.nom.trim() !== '' && opt.nom === newMatName) ? newMatName : undefined}
                  onValueChange={value => {
                    setNewMatName(value);
                    setNewMatUnit(getUniteForMaterial(value, newMatType));
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Choisir la matière" />
                  </SelectTrigger>
                  <SelectContent>
                    {materialOptions.filter(opt => opt.nom && opt.nom.trim() !== '' && opt.type === newMatType).map(opt => (
                      <SelectItem key={opt.nom} value={opt.nom}>{opt.nom}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>Quantité *</Label>
                <Input value={newMatQty} onChange={e => setNewMatQty(e.target.value)} placeholder="0" type="number" min="0" required />
              </div>
              <div>
                <Label>Unité *</Label>
                <Select
                  value={UNITES.includes(newMatUnit) && newMatUnit.trim() !== '' ? newMatUnit : undefined}
                  onValueChange={setNewMatUnit}
                  disabled>
                  <SelectTrigger>
                    <SelectValue placeholder="Unité" />
                  </SelectTrigger>
                  <SelectContent>
                    {UNITES.filter(u => u && u.trim() !== '').map(u => (
                      <SelectItem key={u} value={u}>{u}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              {/* Hide Type field if product.type is 'finis' or 'semi' (handled by dedicated modals) */}
              {!(product.type === 'finis' || product.type === 'semi') && (
                <div>
                  <Label>Type *</Label>
                  <Select
                    value={newMatType}
                    onValueChange={(value: string) => setNewMatType(value as 'matiere' | 'semi')}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Sélectionner le type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="matiere">Matière première</SelectItem>
                      <SelectItem value="semi">Semi-fini</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => setShowAddMaterialModal(false)}>
                  Annuler
                </Button>
                <Button type="submit">
                  Ajouter
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Add Raw Material Modal (for Add Product, type 'finis') */}
      {showAddRawMaterialModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-6 relative">
            <button
              className="absolute top-3 right-3 text-gray-500 hover:text-black"
              onClick={() => setShowAddRawMaterialModal(false)}
            >
              <X size={20} />
            </button>
            <h2 className="text-xl font-bold mb-4">Ajouter une matière première</h2>
            <form onSubmit={handleAddRawMaterial} className="space-y-4">
              <div>
                <Label>Nom *</Label>
                <Select
                  value={rawMatName}
                  onValueChange={value => {
                    setRawMatName(value);
                    setRawMatUnit(getUniteForMaterial(value, 'matiere'));
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Choisir la matière première" />
                  </SelectTrigger>
                  <SelectContent>
                    {materialOptions.filter(opt => opt.type === 'matiere').map(opt => (
                      <SelectItem key={opt.nom} value={opt.nom}>{opt.nom}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>Quantité *</Label>
                <Input value={rawMatQty} onChange={e => setRawMatQty(e.target.value)} placeholder="0" type="number" min="0" required />
              </div>
              <div>
                <Label>Unité *</Label>
                <Select
                  value={UNITES.includes(rawMatUnit) && rawMatUnit.trim() !== '' ? rawMatUnit : undefined}
                  onValueChange={setRawMatUnit}
                  disabled
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Unité" />
                  </SelectTrigger>
                  <SelectContent>
                    {UNITES.filter(u => u && u.trim() !== '').map(u => (
                      <SelectItem key={u} value={u}>{u}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => setShowAddRawMaterialModal(false)}>
                  Annuler
                </Button>
                <Button type="submit">
                  Ajouter
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Add Semi Product Modal (for Add Product, type 'finis') */}
      {showAddSemiProductModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-6 relative">
            <button
              className="absolute top-3 right-3 text-gray-500 hover:text-black"
              onClick={() => setShowAddSemiProductModal(false)}
            >
              <X size={20} />
            </button>
            <h2 className="text-xl font-bold mb-4">Ajouter un semi-produit</h2>
            <form onSubmit={handleAddSemiProduct} className="space-y-4">
              <div>
                <Label>Nom *</Label>
                <Select
                  value={semiProdName}
                  onValueChange={value => {
                    setSemiProdName(value);
                    setSemiProdUnit(getUniteForMaterial(value, 'semi'));
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Choisir le semi-produit" />
                  </SelectTrigger>
                  <SelectContent>
                    {materialOptions.filter(opt => opt.type === 'semi').map(opt => (
                      <SelectItem key={opt.nom} value={opt.nom}>{opt.nom}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>Quantité *</Label>
                <Input value={semiProdQty} onChange={e => setSemiProdQty(e.target.value)} placeholder="0" type="number" min="0" required />
              </div>
              <div>
                <Label>Unité *</Label>
                <Select
                  value={UNITES.includes(semiProdUnit) && semiProdUnit.trim() !== '' ? semiProdUnit : undefined}
                  onValueChange={setSemiProdUnit}
                  disabled
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Unité" />
                  </SelectTrigger>
                  <SelectContent>
                    {UNITES.filter(u => u && u.trim() !== '').map(u => (
                      <SelectItem key={u} value={u}>{u}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => setShowAddSemiProductModal(false)}>
                  Annuler
                </Button>
                <Button type="submit">
                  Ajouter
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Edit Product Modal */}
      {showEditModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30">
          <div ref={editModalRef} className="bg-white rounded-lg shadow-lg w-full max-w-lg relative max-h-[90vh] flex flex-col custom-scrollbar">
            {/* Sticky Header */}
            <div className="sticky top-0 z-10 bg-white p-6 pb-2 flex items-center justify-between border-b">
              <h2 className="text-xl font-bold">Modifier le produit</h2>
              <button
                className="text-gray-500 hover:text-black ml-4"
                onClick={() => setShowEditModal(false)}
              >
                <X size={20} />
              </button>
            </div>
            {/* Scrollable Content */}
            <div className="overflow-y-auto px-6 pt-2 pb-0 flex-1 min-h-0">
              <form id="edit-product-form" onSubmit={handleEditSave} className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Informations Produit</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="reference">Référence *</Label>
                      <Input
                        id="reference"
                        value={editProduct.reference}
                        onChange={(e) => setEditProduct({ ...editProduct, reference: e.target.value })}
                        placeholder="Entrer la référence"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="nom">Nom *</Label>
                      <Input
                        id="nom"
                        value={editProduct.nom}
                        onChange={(e) => setEditProduct({ ...editProduct, nom: e.target.value })}
                        placeholder="Entrer le nom du produit"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="type">Type *</Label>
                      <Select
                        value={editProduct.type}
                        onValueChange={(value) => setEditProduct({ ...editProduct, type: value })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionner le type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="semi">Semi-fini</SelectItem>
                          <SelectItem value="finis">Fini</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label className="mb-2">Structure des matières premières</Label>
                      <div className="space-y-2">
                        {(!editProduct.materials || editProduct.materials.length === 0) ? (
                          <div className="text-muted-foreground text-sm">Aucune matière ajoutée.</div>
                        ) : (
                          <div className="mb-2 max-h-40 overflow-y-auto border rounded bg-muted/10">
                            <ul>
                              {editProduct.materials.map((mat, idx) => (
                                <li key={idx} className="flex items-center gap-2 border-b py-1 px-2 last:border-b-0">
                                  <span>{mat.name} - {mat.quantity} {mat.unit} ({mat.materialType === 'matiere' ? 'Matière première' : 'Semi-fini'})</span>
                                  <button type="button" className="text-red-500 hover:underline text-xs" onClick={() => handleRemoveMaterialEdit(idx)}>Supprimer</button>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                        {editProduct.type === 'finis' ? (
                          <div className="flex gap-2">
                            <Button type="button" onClick={() => setShowEditAddRawMaterialModal(true)}>
                              Ajouter Matière Première
                            </Button>
                            <Button type="button" onClick={() => setShowEditAddSemiProductModal(true)}>
                              Ajouter Semi-fini
                            </Button>
                          </div>
                        ) : (
                          <Button type="button" onClick={() => setShowEditMaterialModal(true)}>Ajouter</Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </form>
            </div>
            {/* Sticky Footer */}
            <div className="sticky bottom-0 z-10 bg-white p-6 pt-2 border-t flex justify-end gap-2">
              <Button type="button" variant="outline" onClick={() => setShowEditModal(false)}>
                Annuler
              </Button>
              <Button type="submit" form="edit-product-form">
                <Save className="h-4 w-4 mr-2" />
                Sauvegarder
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Add Material Modal for Edit Product */}
      {showEditMaterialModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-6 relative">
            <button
              className="absolute top-3 right-3 text-gray-500 hover:text-black"
              onClick={() => setShowEditMaterialModal(false)}
            >
              <X size={20} />
            </button>
            <h2 className="text-xl font-bold mb-4">Ajouter une matière première</h2>
            <form onSubmit={handleAddMaterialEdit} className="space-y-4">
              <div>
                <Label>Nom *</Label>
                <Select
                  value={materialOptions.some(opt => opt.nom && opt.nom.trim() !== '' && opt.nom === editMatName) ? editMatName : undefined}
                  onValueChange={value => {
                    setEditMatName(value);
                    setEditMatUnit(getUniteForMaterial(value, editMatType));
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Choisir la matière" />
                  </SelectTrigger>
                  <SelectContent>
                    {materialOptions.filter(opt => opt.nom && opt.nom.trim() !== '' && opt.type === editMatType).map(opt => (
                      <SelectItem key={opt.nom} value={opt.nom}>{opt.nom}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>Quantité *</Label>
                <Input value={editMatQty} onChange={e => setEditMatQty(e.target.value)} placeholder="0" type="number" min="0" required />
              </div>
              <div>
                <Label>Unité *</Label>
                <Select
                  value={UNITES.includes(editMatUnit) && editMatUnit.trim() !== '' ? editMatUnit : undefined}
                  onValueChange={setEditMatUnit}
                  disabled>
                  <SelectTrigger>
                    <SelectValue placeholder="Unité" />
                  </SelectTrigger>
                  <SelectContent>
                    {UNITES.filter(u => u && u.trim() !== '').map(u => (
                      <SelectItem key={u} value={u}>{u}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>Type *</Label>
                <Select
                  value={editMatType}
                  onValueChange={(value: string) => setEditMatType(value as 'matiere' | 'semi')}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner le type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="matiere">Matière première</SelectItem>
                    <SelectItem value="semi">Semi-fini</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => setShowEditMaterialModal(false)}>
                  Annuler
                </Button>
                <Button type="submit">
                  Ajouter
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Add Raw Material Modal (for Edit Product, type 'finis') */}
      {showEditAddRawMaterialModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-6 relative">
            <button
              className="absolute top-3 right-3 text-gray-500 hover:text-black"
              onClick={() => setShowEditAddRawMaterialModal(false)}
            >
              <X size={20} />
            </button>
            <h2 className="text-xl font-bold mb-4">Ajouter une matière première</h2>
            <form onSubmit={e => {
              e.preventDefault();
              if (!editRawMatName || !editRawMatQty || isNaN(Number(editRawMatQty))) return;
              setEditProduct({
                ...editProduct,
                materials: [
                  ...(editProduct.materials || []),
                  { name: editRawMatName, quantity: Number(editRawMatQty), unit: editRawMatUnit, materialType: 'matiere' }
                ]
              });
              setEditRawMatName('');
              setEditRawMatQty('');
              setEditRawMatUnit(UNITES[0]);
              setShowEditAddRawMaterialModal(false);
            }} className="space-y-4">
              <div>
                <Label>Nom *</Label>
                <Select
                  value={editRawMatName}
                  onValueChange={value => {
                    setEditRawMatName(value);
                    setEditRawMatUnit(getUniteForMaterial(value, 'matiere'));
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Choisir la matière première" />
                  </SelectTrigger>
                  <SelectContent>
                    {materialOptions.filter(opt => opt.type === 'matiere').map(opt => (
                      <SelectItem key={opt.nom} value={opt.nom}>{opt.nom}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>Quantité *</Label>
                <Input value={editRawMatQty} onChange={e => setEditRawMatQty(e.target.value)} placeholder="0" type="number" min="0" required />
              </div>
              <div>
                <Label>Unité *</Label>
                <Select
                  value={UNITES.includes(editRawMatUnit) && editRawMatUnit.trim() !== '' ? editRawMatUnit : undefined}
                  onValueChange={setEditRawMatUnit}
                  disabled
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Unité" />
                  </SelectTrigger>
                  <SelectContent>
                    {UNITES.filter(u => u && u.trim() !== '').map(u => (
                      <SelectItem key={u} value={u}>{u}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => setShowEditAddRawMaterialModal(false)}>
                  Annuler
                </Button>
                <Button type="submit">
                  Ajouter
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Add Semi Product Modal (for Edit Product, type 'finis') */}
      {showEditAddSemiProductModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/30">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-6 relative">
            <button
              className="absolute top-3 right-3 text-gray-500 hover:text-black"
              onClick={() => setShowEditAddSemiProductModal(false)}
            >
              <X size={20} />
            </button>
            <h2 className="text-xl font-bold mb-4">Ajouter un semi-produit</h2>
            <form onSubmit={e => {
              e.preventDefault();
              if (!editSemiProdName || !editSemiProdQty || isNaN(Number(editSemiProdQty))) return;
              setEditProduct({
                ...editProduct,
                materials: [
                  ...(editProduct.materials || []),
                  { name: editSemiProdName, quantity: Number(editSemiProdQty), unit: editSemiProdUnit, materialType: 'semi' }
                ]
              });
              setEditSemiProdName('');
              setEditSemiProdQty('');
              setEditSemiProdUnit(UNITES[0]);
              setShowEditAddSemiProductModal(false);
            }} className="space-y-4">
              <div>
                <Label>Nom *</Label>
                <Select
                  value={editSemiProdName}
                  onValueChange={value => {
                    setEditSemiProdName(value);
                    setEditSemiProdUnit(getUniteForMaterial(value, 'semi'));
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Choisir le semi-produit" />
                  </SelectTrigger>
                  <SelectContent>
                    {materialOptions.filter(opt => opt.type === 'semi').map(opt => (
                      <SelectItem key={opt.nom} value={opt.nom}>{opt.nom}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>Quantité *</Label>
                <Input value={editSemiProdQty} onChange={e => setEditSemiProdQty(e.target.value)} placeholder="0" type="number" min="0" required />
              </div>
              <div>
                <Label>Unité *</Label>
                <Select
                  value={UNITES.includes(editSemiProdUnit) && editSemiProdUnit.trim() !== '' ? editSemiProdUnit : undefined}
                  onValueChange={setEditSemiProdUnit}
                  disabled
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Unité" />
                  </SelectTrigger>
                  <SelectContent>
                    {UNITES.filter(u => u && u.trim() !== '').map(u => (
                      <SelectItem key={u} value={u}>{u}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => setShowEditAddSemiProductModal(false)}>
                  Annuler
                </Button>
                <Button type="submit">
                  Ajouter
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Show Materials Modal */}
      {showMaterialsModal && selectedProductIdx !== null && (
        <div className="fixed inset-0 flex items-center justify-center bg-black/30">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-lg p-6 relative">
            <button
              className="absolute top-3 right-3 text-gray-500 hover:text-black"
              onClick={() => setShowMaterialsModal(false)}
            >
              <X size={20} />
            </button>
            <h2 className="text-xl font-bold mb-4">Matières utilisées pour {products[selectedProductIdx].nom}</h2>
            
            {/* Filter buttons */}
            <div className="flex gap-2 mb-4">
              <Button 
                type="button" 
                variant={showMaterialsList.some(mat => mat.materialType === 'matiere') ? "default" : "outline"}
                onClick={() => setShowMaterialsList(showMaterialsList.filter(mat => mat.materialType === 'matiere'))}
              >
                Matières Premières
              </Button>
              {showMaterialsList.some(mat => mat.materialType === 'semi') && (
                <Button 
                  type="button" 
                  variant={showMaterialsList.some(mat => mat.materialType === 'semi') ? "default" : "outline"}
                  onClick={() => setShowMaterialsList(showMaterialsList.filter(mat => mat.materialType === 'semi'))}
                >
                  Semi-finis
                </Button>
              )}
              <Button 
                type="button" 
                variant="outline"
                onClick={() => {
                  setShowMaterialsList(allMaterialsList);
                }}
              >
                Tous
              </Button>
            </div>
            
            {showMaterialsList.length === 0 ? (
              <div className="text-muted-foreground mb-4">Aucune matière n'est définie pour ce produit.</div>
            ) : (
              <ul className="mb-4">
                {showMaterialsList.map((mat, i) => (
                  <li key={mat.material_id + '-' + i} className="flex flex-col border-b py-2">
                    <span>
                      <b>Nom:</b> {mat.name || '-'} &nbsp; 
                      <b>Qte:</b> {mat.quantity} {mat.unit || '-'} &nbsp; 
                      <b>Type:</b> {mat.materialType === 'matiere' ? 'Matière première' : 'Semi-fini'}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      Product ID: {mat.product_id} | Material ID: {mat.material_id}
                    </span>
                  </li>
                ))}
              </ul>
            )}
          </div>
        </div>
      )}
      {/* Custom scrollbar style for modal: visible only while scrolling */}
      <style>{`
        .custom-scrollbar::-webkit-scrollbar {
          width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: transparent;
          border-radius: 4px;
          transition: background 0.3s;
        }
        .custom-scrollbar.scrolling::-webkit-scrollbar-thumb {
          background: rgba(0,0,0,0.12);
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: transparent;
        }
        .custom-scrollbar {
          scrollbar-width: thin;
          scrollbar-color: transparent transparent;
          transition: scrollbar-color 0.3s;
        }
        .custom-scrollbar.scrolling {
          scrollbar-color: rgba(0,0,0,0.12) transparent;
        }
      `}</style>
    </Layout>
  );
};

export default ProductMaterials; 