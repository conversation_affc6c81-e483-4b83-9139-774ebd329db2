import React, { useState, useEffect, useRef } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { X, ArrowLeft } from 'lucide-react';
import { toast } from 'sonner';
import axios from 'axios';

interface ProductOperationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  operationType: 'Entrée' | 'Sortie' | '';
  onSuccess: (info: any) => void;
  product: any;
  locations: string[];
  floorData: Record<string, Array<{ id: number; name: string; availableCapacity: number; totalCapacity: number }>>;
  movementHistory?: any[]; // <-- Add this prop
}

const FiniOperationClient: React.FC<ProductOperationDialogProps> = ({
  open,
  onOpenChange,
  operationType,
  onSuccess,
  product: initialProduct,
  locations,
  floorData,
  movementHistory = [],
}) => {
  // --- State ---
  const [finisProducts, setFinisProducts] = useState<any[]>([]);
  const [product, setProduct] = useState<any>(initialProduct || null);
  // Product search
  const [productSearch, setProductSearch] = useState('');

  const [operationForm, setOperationForm] = useState({
    quantity: '',
    location: '',
    floor: '',
    floorQuantity: '',
    fabricationDate: '', // new
    expirationDate: '',   // new
    batchNumber: '' // new
  });
  // Entrée
  const [showFloorSelection, setShowFloorSelection] = useState(false);
  const [selectedFloors, setSelectedFloors] = useState<Array<{ floorId: number, floorName: string, quantity: number, availableCapacity: number, type?: string }>>([]);
  const [selectedZones, setSelectedZones] = useState<Array<{ zone: string, floors: Array<{ floorId: number, floorName: string, quantity: number, availableCapacity: number, type?: string }> }>>([]);
  const [currentZoneIndex, setCurrentZoneIndex] = useState(0);
  const [showZoneSelection, setShowZoneSelection] = useState(false);
  // Sortie
  const [selectedExitFloors, setSelectedExitFloors] = useState<Array<{ floorId: number, floorName: string, quantity: number, availableCapacity: number, maxCapacity?: number, places?: number }>>([]);
  const [selectedExitZones, setSelectedExitZones] = useState<Array<{ zone: string, floors: Array<{ floorId: number, floorName: string, quantity: number, availableCapacity: number, maxCapacity?: number, places?: number }> }>>([]);
  const [currentExitZoneIndex, setCurrentExitZoneIndex] = useState(0);
  const [showExitZoneSelection, setShowExitZoneSelection] = useState(false);
  // Materials for production
  const [productMaterials, setProductMaterials] = useState<Array<{ name: string; quantity: number; unit: string }>>([]);
  // --- New: All matiere movements for stock calculation
  const [matiereMovements, setMatiereMovements] = useState<any[]>([]);
  // --- New: Per-material movements for stock calculation
  const [materialMovements, setMaterialMovements] = useState<Record<string, any[]>>({});

  // --- Location ID Map ---
  const [locationIdMap, setLocationIdMap] = useState<Record<string, number>>({});
  useEffect(() => {
    // Fetch locations with ids for mapping name to id
    axios.get('/api/locations').then(res => {
      const map: Record<string, number> = {};
      const floorDataPatched: Record<string, Array<{ id: number, name: string, availableCapacity: number, totalCapacity: number, type?: 'etage' | 'part' }>> = {};
      res.data.forEach((loc: any) => {
        map[loc.name] = loc.id;
        let items: Array<{ id: number, name: string, availableCapacity: number, totalCapacity: number, type?: 'etage' | 'part' }> = [];
        if (loc.etages && Array.isArray(loc.etages) && loc.etages.length > 0) {
          items = loc.etages.map((item: any) => ({
            id: item.id,
            name: item.name,
            availableCapacity: item.currentStock !== undefined ? (item.places || 0) - item.currentStock : (item.places || 0),
            totalCapacity: item.places || 0,
            type: 'etage',
          }));
        } else if (loc.parts && Array.isArray(loc.parts) && loc.parts.length > 0) {
          items = loc.parts.map((item: any) => ({
            id: item.id,
            name: item.name,
            availableCapacity: item.currentStock !== undefined ? (item.maxCapacity || 0) - item.currentStock : (item.maxCapacity || 0),
            totalCapacity: item.maxCapacity || 0,
            type: 'part',
          }));
        }
        floorDataPatched[loc.name] = items;
      });
      setLocationIdMap(map);
      // Do not call setFloorData here, as floorData is a prop.
    });
  }, []);

  // Fetch products of type 'finis' when dialog opens
  useEffect(() => {
    if (open) {
      fetch('/api/products?type=finis')
        .then(res => res.json())
        .then(data => setFinisProducts(data))
        .catch(() => setFinisProducts([]));
    }
  }, [open]);

  // If initialProduct changes, update product state
  useEffect(() => {
    if (initialProduct) setProduct(initialProduct);
  }, [initialProduct]);

  // Fetch product materials when dialog opens and operationType is 'Entrée'
  useEffect(() => {
    if (open && operationType === 'Entrée' && product?.id) {
      axios.get(`/api/products/${product.id}/materials`)
        .then(res => setProductMaterials(res.data))
        .catch(() => setProductMaterials([]));
    }
  }, [open, operationType, product?.id]);

  // Fetch all matiere movements when dialog opens and operationType is 'Entrée'
  useEffect(() => {
    if (open && operationType === 'Entrée') {
      axios.get('/api/movements/ready')
        .then(res => setMatiereMovements(res.data))
        .catch(() => setMatiereMovements([]));
    }
  }, [open, operationType]);

  // Fetch material movements for all materials when productMaterials changes
  useEffect(() => {
    if (productMaterials.length > 0) {
      productMaterials.forEach(async (material) => {
        if (!materialMovements[material.name]) {
          try {
            const response = await axios.get(`/api/movements/materials?material_name=${encodeURIComponent(material.name)}`);
            setMaterialMovements(prev => ({ ...prev, [material.name]: response.data }));
          } catch (error) {
            console.error('Error fetching material movements:', error);
            setMaterialMovements(prev => ({ ...prev, [material.name]: [] }));
          }
        }
      });
    }
  }, [productMaterials]);

  // --- Helpers ---
  const getAvailableFloors = (location: string) => {
    return floorData[location as keyof typeof floorData] || [];
  };
  const getTotalAvailableCapacity = (location: string) => {
    const floors = getAvailableFloors(location);
    return floors.reduce((sum, floor) => sum + floor.availableCapacity, 0);
  };
  const getTotalAvailableCapacityAllZones = (zones: typeof selectedZones | typeof selectedExitZones) => {
    return zones.reduce((sum, zone) =>
      sum + getTotalAvailableCapacity(zone.zone), 0
    );
  };
  // Distribute quantity across zones/floors
  function distributeQuantityAcrossZones(totalQuantity: number, zones: typeof selectedZones) {
    let remaining = totalQuantity;
    return zones.map(zone => {
      const newFloors = getAvailableFloors(zone.zone).map(floor => {
        if (remaining <= 0) return { floorId: floor.id, floorName: floor.name, quantity: 0, availableCapacity: floor.availableCapacity };
        const qty = Math.min(remaining, floor.availableCapacity);
        remaining -= qty;
        return { floorId: floor.id, floorName: floor.name, quantity: qty, availableCapacity: floor.availableCapacity };
      });
      return { zone: zone.zone, floors: newFloors };
    });
  }
  function distributeQuantityAcrossExitZones(totalQuantity: number, zones: typeof selectedExitZones) {
    let remaining = totalQuantity;
    return zones.map(zone => {
      const newFloors = getAvailableFloors(zone.zone).map(floor => {
        if (remaining <= 0) return { floorId: floor.id, floorName: floor.name, quantity: 0, availableCapacity: floor.availableCapacity };
        const qty = Math.min(remaining, floor.availableCapacity);
        remaining -= qty;
        return { floorId: floor.id, floorName: floor.name, quantity: qty, availableCapacity: floor.availableCapacity };
      });
      return { zone: zone.zone, floors: newFloors };
    });
  }
  // FIFO allocation for sortie (grouped by lot)
  function fifoAllocateGroupedByLot(quantity: number, stock: any) { // Changed to 'any' as detailedSourceStock is removed
    let remaining = quantity;
    const allParts = stock.flatMap(z => z.parts.map((p, idx) => ({
      zone: z.zone,
      part: p.name,
      available: p.available,
      lot: p.lot,
      order: idx,
    })));
    allParts.sort((a, b) => a.lot.localeCompare(b.lot));
    const allocationByLot: Record<string, Array<{ zone: string; part: string; taken: number; available: number }>> = {};
    let totalTaken = 0;
    for (const part of allParts) {
      if (remaining <= 0) break;
      const take = Math.min(part.available, remaining);
      if (take > 0) {
        if (!allocationByLot[part.lot]) allocationByLot[part.lot] = [];
        allocationByLot[part.lot].push({ zone: part.zone, part: part.part, taken: take, available: part.available });
        remaining -= take;
        totalTaken += take;
      }
    }
    return { allocationByLot, totalTaken };
  }

  // Compute FIFO stock for materials needed for production
  function computeMaterialsFifoStock(materialName: string, movements: any[] = []) {
    // Only consider 'Entrée' and 'Sortie' for this material
    const materialMovements = movements.filter(m => 
      m.status === 'Entrée' || m.status === 'Sortie'
    );
    
    // Group by lot/location/etage/part
    type StockKey = string;
    function makeKey(m: any) {
      return [m.batch_number || m.lot || '', m.location_name || '', m.etage_name || '', m.part_id || ''].join('||');
    }
    
    // Sum entries
    const stockMap: Record<StockKey, { lot: string, location: string, etage: string, part: string, entree: number, sortie: number, available: number }> = {};
    for (const m of materialMovements) {
      const key = makeKey(m);
      if (!stockMap[key]) {
        stockMap[key] = {
          lot: m.batch_number || m.lot || '',
          location: m.location_name || '',
          etage: m.etage_name || '',
          part: m.part_id || '',
          entree: 0,
          sortie: 0,
          available: 0,
        };
      }
      if (m.status === 'Entrée') {
        stockMap[key].entree += m.quantity;
        stockMap[key].available += m.quantity;
      } else if (m.status === 'Sortie') {
        stockMap[key].sortie += m.quantity;
        stockMap[key].available -= m.quantity;
      }
    }
    
    // Sort entries by lot (FIFO)
    const entryList = Object.entries(stockMap).map(([key, val]) => ({ key, ...val }));
    entryList.sort((a, b) => a.lot.localeCompare(b.lot));
    // Only keep lots with available > 0
    return entryList.filter(e => e.available > 0);
  }

  // --- Helper: Calculate available stock for matiere by product/location (ignore etage/part/batch) ---
  function calculateMatiereAvailableStockSimple() {
    // Group by: product_id, location_id
    const stockMap = new Map();
    for (const m of matiereMovements) {
      const key = [m.product_id, m.location_id].join('||');
      if (!stockMap.has(key)) {
        stockMap.set(key, {
          product_id: m.product_id,
          location_id: m.location_id,
          entree: 0,
          sortie: 0,
        });
      }
      const entry = stockMap.get(key);
      if (m.status === 'Entrée') {
        entry.entree += m.quantity;
      } else if (m.status === 'Sortie') {
        entry.sortie += m.quantity;
      }
    }
    // Calculate available
    return Array.from(stockMap.values()).map(e => ({
      ...e,
      available: e.entree - e.sortie
    })).filter(e => e.available > 0);
  }

  // Helper to generate a batch number
  function generateBatchNumber() {
    const now = new Date();
    const pad = (n) => n.toString().padStart(2, '0');
    const datePart = `${now.getFullYear()}${pad(now.getMonth() + 1)}${pad(now.getDate())}`;
    const timePart = `${pad(now.getHours())}${pad(now.getMinutes())}${pad(now.getSeconds())}`;
    const rand = Math.floor(1000 + Math.random() * 9000);
    return `LOT-${datePart}-${timePart}-${rand}`;
  }

  // --- Entrée Handlers ---
  const handleLocationChange = (location: string) => {
    setOperationForm(prev => ({ ...prev, location }));
    if (operationType === 'Entrée' && location) {
      setShowFloorSelection(true);
      setShowZoneSelection(false);
      const availableFloors = getAvailableFloors(location);
      setSelectedFloors(availableFloors.map(floor => ({
        floorId: floor.id,
        floorName: floor.name,
        quantity: 0,
        availableCapacity: floor.availableCapacity,
        type: (floor as any).type, // type may be undefined
      })));
      setSelectedZones([{
        zone: location,
        floors: availableFloors.map(floor => ({
          floorId: floor.id,
          floorName: floor.name,
          quantity: 0,
          availableCapacity: floor.availableCapacity,
          type: (floor as any).type, // type may be undefined
        }))
      }]);
      setCurrentZoneIndex(0);
    } else {
      setShowFloorSelection(false);
      setShowZoneSelection(false);
    }
  };
  const handleFloorSelection = (floorId: number, floorName: string, availableCapacity: number, type?: 'etage' | 'part') => {
    const existingFloor = selectedFloors.find(f => f.floorId === floorId);
    if (existingFloor) {
      setSelectedFloors(prev => prev.filter(f => f.floorId !== floorId));
    } else {
      setSelectedFloors(prev => [...prev, { floorId, floorName, quantity: 0, availableCapacity, type }]);
    }
  };
  const handleFloorQuantityChange = (floorId: number, quantity: number) => {
    setSelectedFloors(prev =>
      prev.map(floor =>
        floor.floorId === floorId
          ? { ...floor, quantity: Math.min(quantity, floor.availableCapacity) }
          : floor
      )
    );
  };
  const handleTotalQuantityChange = (quantity: string) => {
    setOperationForm(prev => ({ ...prev, quantity }));
    if (selectedZones.length > 0 && quantity && parseInt(quantity) > 0) {
      const distributed = distributeQuantityAcrossZones(parseInt(quantity), selectedZones);
      setSelectedZones(distributed);
      setSelectedFloors(distributed[currentZoneIndex]?.floors || []);
    }
  };
  const switchToZone = (zoneIndex: number) => {
    setCurrentZoneIndex(zoneIndex);
    if (selectedZones[zoneIndex]) {
      setOperationForm(prev => ({ ...prev, location: selectedZones[zoneIndex].zone }));
      setSelectedFloors(selectedZones[zoneIndex].floors);
    }
  };
  const addNewZone = (newZone: string) => {
    setSelectedZones(prev => {
      const updated = [...prev, { zone: newZone, floors: getAvailableFloors(newZone).map(floor => ({ floorId: floor.id, floorName: floor.name, quantity: 0, availableCapacity: floor.availableCapacity })) }];
      const distributed = distributeQuantityAcrossZones(parseInt(operationForm.quantity) || 0, updated);
      setCurrentZoneIndex(distributed.length - 1);
      setSelectedFloors(distributed[distributed.length - 1].floors);
      setOperationForm(prevForm => ({ ...prevForm, location: newZone }));
      return distributed;
    });
    setShowZoneSelection(false);
  };
  const removeZone = (zoneIndex: number) => {
    setSelectedZones(prev => {
      const newZones = prev.filter((_, idx) => idx !== zoneIndex);
      if (newZones.length === 0) {
        setSelectedFloors([]);
        setCurrentZoneIndex(0);
        setOperationForm(prev => ({ ...prev, location: '' }));
        return [];
      } else {
        const distributed = distributeQuantityAcrossZones(parseInt(operationForm.quantity) || 0, newZones);
        const newCurrent = zoneIndex === 0 ? 0 : zoneIndex - 1;
        setCurrentZoneIndex(newCurrent);
        setSelectedFloors(distributed[newCurrent].floors);
        setOperationForm(prev => ({ ...prev, location: distributed[newCurrent].zone }));
        return distributed;
      }
    });
  };
  const handleManualDistribute = () => {
    if (!operationForm.quantity || selectedZones.length === 0) return;
    const distributed = distributeQuantityAcrossZones(parseInt(operationForm.quantity) || 0, selectedZones);
    setSelectedZones(distributed);
    if (distributed[currentZoneIndex]) {
      // Auto-select the first available floor and set its quantity to the total requested quantity
      const firstFloor = distributed[currentZoneIndex].floors[0];
      if (firstFloor) {
        setSelectedFloors([
          {
            ...firstFloor,
            quantity: parseInt(operationForm.quantity) || 0,
          },
        ]);
      } else {
        setSelectedFloors([]);
      }
    } else {
      setSelectedFloors([]);
    }
  };
  const totalSelectedQuantity = selectedZones.reduce((sum, zone) =>
    sum + zone.floors.reduce((zoneSum, floor) => zoneSum + floor.quantity, 0), 0
  );

  // --- Sortie Handlers ---
  const handleExitLocationChange = (location: string) => {
    setOperationForm(prev => ({ ...prev, location }));
    if (operationType === 'Sortie' && location) {
      const availableFloors = getAvailableFloors(location);
      setSelectedExitFloors(availableFloors.map(floor => ({
        floorId: floor.id,
        floorName: floor.name,
        quantity: 0,
        availableCapacity: floor.availableCapacity,
        maxCapacity: (floor as any).maxCapacity,
        places: (floor as any).places,
      })));
      setSelectedExitZones([{
        zone: location,
        floors: availableFloors.map(floor => ({
          floorId: floor.id,
          floorName: floor.name,
          quantity: 0,
          availableCapacity: floor.availableCapacity,
          maxCapacity: (floor as any).maxCapacity,
          places: (floor as any).places,
        }))
      }]);
      setCurrentExitZoneIndex(0);
      setShowExitZoneSelection(false);
    } else {
      setSelectedExitFloors([]);
      setShowExitZoneSelection(false);
    }
  };
  const handleExitFloorSelection = (floorId: number, floorName: string, availableCapacity: number) => {
    const existingFloor = selectedExitFloors.find(f => f.floorId === floorId);
    if (existingFloor) {
      setSelectedExitFloors(prev => prev.filter(f => f.floorId !== floorId));
    } else {
      setSelectedExitFloors(prev => [...prev, { floorId, floorName, quantity: 0, availableCapacity, maxCapacity: (selectedExitFloors[0] as any).maxCapacity, places: (selectedExitFloors[0] as any).places }]);
    }
  };
  const handleExitFloorQuantityChange = (floorId: number, quantity: number) => {
    setSelectedExitFloors(prev =>
      prev.map(floor =>
        floor.floorId === floorId
          ? { ...floor, quantity: Math.min(quantity, floor.availableCapacity) }
          : floor
      )
    );
  };
  const addNewExitZone = (newZone: string) => {
    setSelectedExitZones(prev => {
      const updated = [...prev, { zone: newZone, floors: getAvailableFloors(newZone).map(floor => ({ floorId: floor.id, floorName: floor.name, quantity: 0, availableCapacity: floor.availableCapacity })) }];
      const distributed = distributeQuantityAcrossExitZones(parseInt(operationForm.quantity) || 0, updated);
      setCurrentExitZoneIndex(distributed.length - 1);
      setSelectedExitFloors(distributed[distributed.length - 1].floors);
      setOperationForm(prevForm => ({ ...prevForm, location: newZone }));
      return distributed;
    });
    setShowExitZoneSelection(false);
  };
  const removeExitZone = (zoneIndex: number) => {
    setSelectedExitZones(prev => {
      const newZones = prev.filter((_, idx) => idx !== zoneIndex);
      if (newZones.length === 0) {
        setSelectedExitFloors([]);
        setCurrentExitZoneIndex(0);
        setOperationForm(prev => ({ ...prev, location: '' }));
        return [];
      } else {
        const distributed = distributeQuantityAcrossExitZones(parseInt(operationForm.quantity) || 0, newZones);
        const newCurrent = zoneIndex === 0 ? 0 : zoneIndex - 1;
        setCurrentExitZoneIndex(newCurrent);
        setSelectedExitFloors(distributed[newCurrent].floors);
        setOperationForm(prev => ({ ...prev, location: distributed[newCurrent].zone }));
        return distributed;
      }
    });
  };
  const handleExitManualDistribute = () => {
    if (!operationForm.quantity || selectedExitZones.length === 0) return;
    const distributed = distributeQuantityAcrossExitZones(parseInt(operationForm.quantity) || 0, selectedExitZones);
    setSelectedExitZones(distributed);
    if (distributed[currentExitZoneIndex]) {
      setSelectedExitFloors(distributed[currentExitZoneIndex].floors);
    } else {
      setSelectedExitFloors([]);
    }
  };
  const handleExitTotalQuantityChange = (quantity: string) => {
    setOperationForm(prev => ({ ...prev, quantity }));
  };
  const totalSelectedExitQuantity = selectedExitZones.reduce((sum, zone) =>
    sum + zone.floors.reduce((zoneSum, floor) => zoneSum + floor.quantity, 0), 0
  );

  // --- FIFO Stock Calculation for Sortie ---
  // Build current stock by lot/location/etage/part using movementHistory
  function computeFifoStock() {
    // Only consider 'Entrée' and 'Sortie' for this product
    const entries = movementHistory.filter(m => m.status === 'Entrée');
    const sorties = movementHistory.filter(m => m.status === 'Sortie');
    // Group by lot/location/etage/part
    type StockKey = string;
    function makeKey(m: any) {
      return [m.batch_number || m.lot || '', m.location_name || '', m.etage_name || '', m.part_id || ''].join('||');
    }
    // Sum entries
    const stockMap: Record<StockKey, { lot: string, location: string, etage: string, part: string, available: number }> = {};
    for (const m of entries) {
      const key = makeKey(m);
      if (!stockMap[key]) {
        stockMap[key] = {
          lot: m.batch_number || m.lot || '',
          location: m.location_name || '',
          etage: m.etage_name || '',
          part: m.part_id || '',
          available: 0,
        };
      }
      stockMap[key].available += m.quantity;
    }
    // Subtract sorties FIFO (by lot)
    // Sort entries by date/time (FIFO)
    const entryList = Object.entries(stockMap).map(([key, val]) => ({ key, ...val }));
    entryList.sort((a, b) => a.lot.localeCompare(b.lot));
    // For each sortie, subtract from earliest lots first
    for (const sortie of sorties) {
      let qty = sortie.quantity;
      for (const entry of entryList) {
        if (qty <= 0) break;
        if (
          (sortie.location_name === entry.location) &&
          (sortie.etage_name === entry.etage) &&
          (String(sortie.part_id || '') === String(entry.part))
        ) {
          const take = Math.min(entry.available, qty);
          entry.available -= take;
          qty -= take;
        }
      }
    }
    // Only keep lots with available > 0
    return entryList.filter(e => e.available > 0);
  }
  // FIFO allocation for requested sortie
  function fifoAllocateSortie(requestedQty: number, fifoStock: ReturnType<typeof computeFifoStock>) {
    let remaining = requestedQty;
    const allocation: Array<{ lot: string, location: string, etage: string, part: string, taken: number, available: number }> = [];
    for (const stock of fifoStock) {
      if (remaining <= 0) break;
      const take = Math.min(stock.available, remaining);
      allocation.push({ ...stock, taken: take, available: stock.available });
      remaining -= take;
    }
    return { allocation, remaining };
  }

  // --- Reset on close ---
  useEffect(() => {
    if (!open) {
      setOperationForm({ quantity: '', location: '', floor: '', floorQuantity: '', fabricationDate: '', expirationDate: '', batchNumber: '' });
      setShowFloorSelection(false);
      setSelectedFloors([]);
      setSelectedZones([]);
      setCurrentZoneIndex(0);
      setShowZoneSelection(false);
      setSelectedExitFloors([]);
      setSelectedExitZones([]);
      setCurrentExitZoneIndex(0);
      setShowExitZoneSelection(false);
    } else {
      // Set fabricationDate to today and generate batch number when dialog opens
      setOperationForm(prev => ({
        ...prev,
        fabricationDate: new Date().toISOString().slice(0, 10),
        expirationDate: '',
        batchNumber: generateBatchNumber()
      }));
    }
  }, [open]);

  // --- Save Handler ---
  const handleSave = async () => {
    const isEntry = operationType === 'Entrée';
    const isExit = operationType === 'Sortie';
    const location_id = locationIdMap[operationForm.location];
    const product_id = product?.id;
    const product_type = 'ready';
    const fabricationDate = operationForm.fabricationDate || new Date().toISOString().slice(0, 10);
    const expirationDate = operationForm.expirationDate || fabricationDate;
    const status = isEntry ? 'Entrée' : 'Sortie';
    const quantity = parseInt(operationForm.quantity);

    // Aggregate all selected floors from all selected zones (for Entrée)
    const allSelectedFloors = isEntry
      ? selectedZones.flatMap(zone => zone.floors.filter(floor => floor.quantity > 0).map(floor => ({
          ...floor,
          zone: zone.zone
        })))
      : selectedExitZones.flatMap(zone => zone.floors.filter(floor => floor.quantity > 0).map(floor => ({
          ...floor,
          zone: zone.zone
        })));

    // Type guards for floor
    function hasMaxCapacity(obj: any, obj2?: any): obj is { maxCapacity: number } {
      return (obj && typeof obj === 'object' && 'maxCapacity' in obj) || (obj2 && typeof obj2 === 'object' && 'maxCapacity' in obj2);
    }
    function hasPlaces(obj: any, obj2?: any): obj is { places: number } {
      return (obj && typeof obj === 'object' && 'places' in obj) || (obj2 && typeof obj2 === 'object' && 'places' in obj2);
    }
    function isPart(floor: any, floorObj?: any) {
      return ((floorObj && floorObj.type === 'part') || (floor && floor.type === 'part')) || hasMaxCapacity(floor, floorObj);
    }
    function isEtage(floor: any, floorObj?: any) {
      return ((floorObj && floorObj.type === 'etage') || (floor && floor.type === 'etage')) || hasPlaces(floor, floorObj);
    }
    // Debug log for all relevant values
    console.log('[FiniOperationClient] handleSave debug:', {
      operationType,
      product,
      product_id,
      product_type,
      location: operationForm.location,
      location_id,
      fabricationDate,
      expirationDate,
      status,
      quantity,
      allSelectedFloors,
      selectedZones,
      selectedExitZones,
      operationForm
    });

    if (!product_id || !location_id || !quantity) {
      if (operationType === 'Entrée') {
        toast.error('Veuillez remplir tous les champs obligatoires.');
        return;
      } else if (operationType === 'Sortie') {
        if (!quantity) {
          toast.error('Veuillez saisir la quantité.');
          return;
        }
        // For Sortie, do not require location_id or product_id (if product_id is always present)
      }
    }

    try {
      if (operationType === 'Entrée') {
        // 1. Insert Entrée movement for the product (fini or semi)
        const entryProductPromises = allSelectedFloors
          .map(floor => {
            let floorObj = null;
            if (floor.zone && floorData[floor.zone]) {
              floorObj = floorData[floor.zone].find(f => f.id === floor.floorId);
            }
            // Determine type from type property or name
            const type = (floor.type || (floorObj && floorObj.type)) || '';
            const name = (floor.floorName || (floorObj && floorObj.name) || '').toLowerCase();
            const isEtageType = type === 'etage' || name.includes('étage');
            const isPartType = type === 'part' || name.includes('partie');
            const movement = {
              product_type: 'ready',
              product_id,
              status: 'Entrée',
              quantity: floor.quantity,
              location_id: locationIdMap[floor.zone],
              fabricationDate,
              expirationDate,
              date: fabricationDate,
              etage_id: isEtageType ? floor.floorId : null,
              part_id: isPartType ? floor.floorId : null,
              batch_number: operationForm.batchNumber || generateBatchNumber(),
            };
            // Debug logs for diagnosis
            console.log('[DEBUG][Finis] Selected floor:', floor);
            console.log('[DEBUG][Finis] Floor object from floorData:', floorObj);
            console.log('[DEBUG][Finis] Movement to send:', movement);
            return axios.post('/api/movements', movement);
          });
        // 2. For each BOM item, implement FIFO sortie logic for matiere and semi
        const bomPromises: Promise<any>[] = [];
        if (productMaterials.length > 0 && quantity > 0) {
          for (const material of productMaterials) {
            let materialType = 'matiere';
            const matAny = material as any;
            if (matAny.material_type) {
              materialType = String(matAny.material_type);
            } else if (matAny.materialType) {
              materialType = String(matAny.materialType);
            }
            const requiredQuantity = quantity * material.quantity;
            try {
              // Get the material product by name and type
              const materialResponse = await axios.get(`/api/products?type=${encodeURIComponent(materialType)}&name=${encodeURIComponent(material.name)}`);
              const materials = materialResponse.data;
              if (materials.length > 0) {
                const materialId = materials[0].id;
                // Fetch FIFO stock for this material (all Entrée movements with available stock)
                const movementRes = await axios.get(`/api/movements/materials?material_name=${encodeURIComponent(material.name)}`);
                const allMovements = movementRes.data;
                const entreeMovements = allMovements.filter(m => m.status === 'Entrée');
                let remaining = requiredQuantity;
                for (const entry of entreeMovements) {
                  if (remaining <= 0) break;
                  // Calculate how much is still available in this lot/etage/part
                  const used = allMovements
                    .filter(m => m.status === 'Sortie' && m.etage_id === entry.etage_id && m.part_id === entry.part_id && m.batch_number === entry.batch_number)
                    .reduce((sum, m) => sum + m.quantity, 0);
                  const available = entry.quantity - used;
                  if (available <= 0) continue;
                  const take = Math.min(available, remaining);
                  const sortieMovement = {
                    product_type: 'ready',
                    product_id: materialId,
                    status: 'Sortie',
                    quantity: take,
                    location_id: entry.location_id,
                    fabricationDate,
                    expirationDate,
                    date: fabricationDate,
                    etage_id: entry.etage_id || null,
                    part_id: entry.part_id || null,
                    batch_number: entry.batch_number || operationForm.batchNumber || generateBatchNumber(),
                  };
                  console.log(`[DEBUG] FIFO Sortie movement for ${materialType}:`, sortieMovement);
                  bomPromises.push(
                    axios.post('/api/movements', sortieMovement)
                  );
                  remaining -= take;
                }
                if (remaining > 0) {
                  toast.error(`Stock insuffisant pour le composant: ${material.name}`);
                }
              }
            } catch (error) {
              console.error(`Error finding material ${material.name}:`, error);
            }
          }
        }
        await Promise.all([...entryProductPromises, ...bomPromises]);
        // Update local floorData state for Entrée
        setTimeout(() => {
          setOperationForm(prev => ({ ...prev, quantity: '', location: '', floor: '', floorQuantity: '' }));
          setSelectedFloors([]);
          setSelectedZones([]);
          setCurrentZoneIndex(0);
          setShowFloorSelection(false);
          setShowZoneSelection(false);
          if (operationForm.location && floorData[operationForm.location]) {
            const updatedFloors = floorData[operationForm.location].map(floor => {
              const selected = selectedFloors.find(f => f.floorId === floor.id);
              if (selected) {
                return {
                  ...floor,
                  availableCapacity: floor.availableCapacity - selected.quantity
                };
              }
              return floor;
            });
            floorData[operationForm.location] = updatedFloors;
          }
        }, 0);
      } else if (operationType === 'Sortie') {
        // FIFO allocation for sortie: create a movement for each floor/part involved
        // Compute available stock per lot/location/etage/part: Entrée - Sortie
        const entries = movementHistory.filter(m => m.status === 'Entrée');
        const sorties = movementHistory.filter(m => m.status === 'Sortie');
        type StockKey = string;
        function makeKey(m: any) {
          return [m.batch_number || m.lot || '', m.location_id || '', m.etage_id || '', m.part_id || ''].join('||');
        }
        // Sum entries
        const stockMap: Record<StockKey, { lot: string, location_id: number, etage_id: number|null, part_id: number|null, available: number, batch_number: string|null }> = {};
        for (const m of entries) {
          const key = [m.batch_number || m.lot || '', m.location_id || '', m.etage_id || '', m.part_id || ''].join('||');
          if (!stockMap[key]) {
            stockMap[key] = {
              lot: m.batch_number || m.lot || '',
              location_id: m.location_id,
              etage_id: m.etage_id !== undefined ? m.etage_id : null,
              part_id: m.part_id !== undefined ? m.part_id : null,
              available: 0,
              batch_number: m.batch_number || m.lot || null,
            };
          }
          stockMap[key].available += m.quantity;
        }
        for (const m of sorties) {
          const key = makeKey(m);
          if (!stockMap[key]) continue;
          stockMap[key].available -= m.quantity;
        }
        let entryList = Object.values(stockMap).filter(e => e.available > 0);
        entryList.sort((a, b) => a.lot.localeCompare(b.lot));
        let remaining = parseInt(operationForm.quantity);
        const sortieMovements = [];
        // Use the new endpoint for ready product sorties
        for (const stock of entryList) {
          if (remaining <= 0) break;
          const take = Math.min(stock.available, remaining);
          const sortiePayload = {
            product_id,
            quantity: take,
            etage_id: stock.etage_id,
            part_id: stock.part_id,
            batch_number: stock.batch_number,
            fabricationDate,
            expirationDate,
            date: fabricationDate,
          };
          sortieMovements.push(
            axios.post('/api/movements/ready-sortie', sortiePayload)
          );
          remaining -= take;
        }
        await Promise.all(sortieMovements);
      }

      toast.success('Mouvement enregistré avec succès.');
      onOpenChange(false);
      onSuccess({
        type: operationType,
        ...operationForm,
        zones: isEntry ? selectedZones : selectedExitZones
      });
    } catch (err: any) {
      console.error('Error saving movements:', err);
      toast.error('Erreur lors de l\'enregistrement du mouvement.');
    }
  };

  // --- Render ---
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="max-w-3xl"
        aria-describedby="dialog-desc"
      >
        <div id="dialog-desc" style={{ position: 'absolute', left: '-9999px', height: 1, width: 1, overflow: 'hidden' }}>
          Effectuez une entrée ou une sortie de stock pour ce produit.
        </div>
        <div className="flex items-center mb-2">
          <Button variant="ghost" size="icon" onClick={() => onOpenChange(false)}>
            <ArrowLeft size={18} />
          </Button>
          <DialogHeader className="flex-1">
            <DialogTitle>{operationType === 'Entrée' ? 'Nouvelle Entrée' : 'Nouvelle Sortie'}</DialogTitle>
          </DialogHeader>
        </div>
        <div className="space-y-4">
          {operationType === 'Entrée' ? (
            <>
              {/* Date de fabrication & expiration */}
              <div className="flex gap-4">
                <div className="flex-1">
                  <label className="block text-sm font-medium mb-1">Date de fabrication</label>
                  <Input
                    type="date"
                    value={operationForm.fabricationDate}
                    onChange={e => setOperationForm(f => ({ ...f, fabricationDate: e.target.value }))}
                    required
                  />
                </div>
                <div className="flex-1">
                  <label className="block text-sm font-medium mb-1">Date d'expiration (optionnelle)</label>
                  <Input
                    type="date"
                    value={operationForm.expirationDate}
                    onChange={e => setOperationForm(f => ({ ...f, expirationDate: e.target.value }))}
                  />
                </div>
              </div>
              {/* Product selection dropdown (moved under date inputs) */}
              <div className="mb-4">
                <label className="block text-sm font-medium mb-1">Produit</label>
                <Select
                  value={product?.id ? String(product.id) : ''}
                  onValueChange={val => {
                    const selected = finisProducts.find(p => String(p.id) === val);
                    setProduct(selected || null);
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner un produit fini" />
                  </SelectTrigger>
                  <SelectContent>
                   {/* Search input for product */}
                   {finisProducts.length > 0 && (
                     <div className="px-2 py-1">
                       <Input
                         type="text"
                         placeholder="Rechercher..."
                         value={productSearch}
                         onChange={e => setProductSearch(e.target.value)}
                         className="mb-2"
                         autoFocus
                       />
                     </div>
                   )}
                    {finisProducts
                     .filter(p => {
                       const search = productSearch.toLowerCase();
                       return (
                         (p.nom && p.nom.toLowerCase().includes(search)) ||
                         (p.reference && p.reference.toLowerCase().includes(search)) ||
                         String(p.id).includes(search)
                       );
                     })
                      .map(p => (
                        <SelectItem key={p.id} value={String(p.id)}>
                          {p.nom || p.reference || p.id}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>
              <Input
                type="number"
                placeholder="Quantité totale"
                value={operationForm.quantity}
                onChange={e => handleTotalQuantityChange(e.target.value)}
              />
              {/* Materials needed for production */}
              {productMaterials.length > 0 && operationForm.quantity && parseInt(operationForm.quantity) > 0 && (
                <div className="mb-4">
                  <h4 className="font-medium mb-3">Matières nécessaires pour la production</h4>
                  <div className="space-y-3">
                    {productMaterials.map((material, index) => {
                      const requiredQuantity = parseInt(operationForm.quantity) * material.quantity;
                      const materialStock = materialMovements[material.name] ? 
                        computeMaterialsFifoStock(material.name, materialMovements[material.name]) : [];
                      const availableQuantity = materialStock.reduce((sum, stock) => sum + stock.available, 0);
                      // --- New: Show real available by location for matiere ---
                      const matiereAvailableStock = calculateMatiereAvailableStockSimple().filter(e => {
                        // Find the product name for this product_id in matiereMovements
                        const anyMovement = matiereMovements.find(m => m.product_id === e.product_id);
                        return anyMovement && anyMovement.product_name === material.name;
                      });
                      return (
                        <div key={index} className="border rounded-lg p-4">
                          <div className="flex items-center justify-between mb-2">
                            <h5 className="font-medium">{material.name}</h5>
                            <div className="text-sm">
                              <span className="text-muted-foreground">Nécessaire: </span>
                              <span className="font-medium">{requiredQuantity} {material.unit}</span>
                            </div>
                          </div>
                          {/* Show real available by location */}
                          {matiereAvailableStock.length > 0 && (
                            <div className="mb-2">
                              <table className="min-w-full text-xs border mb-2">
                                <thead>
                                  <tr>
                                    <th className="border px-2 py-1">Location ID</th>
                                    <th className="border px-2 py-1">Disponible</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {matiereAvailableStock.map((stock, i) => (
                                    <tr key={i}>
                                      <td className="border px-2 py-1">{stock.location_id}</td>
                                      <td className="border px-2 py-1">{stock.available}</td>
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          )}
                          {!materialMovements[material.name] ? (
                            <p className="text-sm text-muted-foreground">Chargement du stock...</p>
                          ) : materialStock.length === 0 ? (
                            <p className="text-sm text-red-600">Aucun stock disponible pour cette matière.</p>
                          ) : (
                            <div>
                              <table className="min-w-full text-xs border mb-2">
                                <thead>
                                  <tr>
                                    <th className="border px-2 py-1">Lot</th>
                                    <th className="border px-2 py-1">Emplacement</th>
                                    <th className="border px-2 py-1">Étage/Partie</th>
                                    <th className="border px-2 py-1">Entrée</th>
                                    <th className="border px-2 py-1">Sortie</th>
                                    <th className="border px-2 py-1">Disponible</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {materialStock.map((stock, i) => (
                                    <tr key={i}>
                                      <td className="border px-2 py-1">{stock.lot}</td>
                                      <td className="border px-2 py-1">{stock.location}</td>
                                      <td className="border px-2 py-1">{stock.etage || stock.part || '-'}</td>
                                      <td className="border px-2 py-1">{stock.entree}</td>
                                      <td className="border px-2 py-1">{stock.sortie}</td>
                                      <td className="border px-2 py-1">{stock.available}</td>
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                              <div className={`text-xs ${
                                availableQuantity >= requiredQuantity 
                                  ? 'text-green-700' 
                                  : 'text-red-600'
                              }`}>
                                {availableQuantity >= requiredQuantity 
                                  ? `✓ Stock suffisant (${availableQuantity} ${material.unit} disponible)` 
                                  : `⚠ Stock insuffisant: il manque ${requiredQuantity - availableQuantity} ${material.unit}`}
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
              <Input
                type="text"
                placeholder="Numéro de lot (Batch Number)"
                value={operationForm.batchNumber}
                onChange={e => setOperationForm(f => ({ ...f, batchNumber: e.target.value }))}
              />
              <Select value={operationForm.location} onValueChange={handleLocationChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner une zone" />
                </SelectTrigger>
                <SelectContent>
                  {locations.map(loc => (
                    <SelectItem key={loc} value={loc}>
                      {loc} ({getTotalAvailableCapacity(loc)} places)
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {showFloorSelection && operationForm.location && (
                <div className="space-y-4">
                  {selectedZones.length > 1 && (
                    <div className="border rounded-lg p-4">
                      <h4 className="font-medium mb-3">Zones sélectionnées</h4>
                      <div className="flex flex-wrap gap-2">
                        {selectedZones.map((zone, index) => (
                          <div key={index} className="flex items-center gap-1">
                            <Button
                              variant={currentZoneIndex === index ? "default" : "outline"}
                              size="sm"
                              onClick={() => switchToZone(index)}
                            >
                              {zone.zone}
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="text-red-500 hover:text-red-700"
                              onClick={() => removeZone(index)}
                              title="Supprimer la zone"
                              aria-label="Supprimer la zone"
                            >
                              <X size={16} />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  <div className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium">Étages disponibles - {operationForm.location}</h4>
                      <div className="text-sm text-muted-foreground">
                        Capacité totale: {getTotalAvailableCapacity(operationForm.location)}
                      </div>
                    </div>
                    <div className="space-y-3">
                      {getAvailableFloors(operationForm.location).map((floor) => (
                        <div key={floor.id} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center space-x-3">
                            <input
                              type="checkbox"
                              checked={selectedFloors.some(f => f.floorId === floor.id)}
                              onChange={() => handleFloorSelection(floor.id, floor.name, floor.availableCapacity, (floor as any).type)}
                              className="rounded"
                            />
                            <div>
                              <p className="font-medium">{floor.name}</p>
                              <p className="text-sm text-muted-foreground">
                                Capacité disponible: {floor.availableCapacity} / {floor.totalCapacity}
                              </p>
                            </div>
                          </div>
                          {selectedFloors.some(f => f.floorId === floor.id) && (
                            <div className="flex items-center space-x-2">
                              <Input
                                type="number"
                                placeholder="Quantité"
                                min="0"
                                max={floor.availableCapacity}
                                value={selectedFloors.find(f => f.floorId === floor.id)?.quantity || 0}
                                onChange={(e) => handleFloorQuantityChange(floor.id, parseInt(e.target.value) || 0)}
                                className="w-24"
                              />
                              <span className="text-sm text-muted-foreground"></span>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                    <div className="mt-4 space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex gap-2">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={handleManualDistribute}
                            disabled={!operationForm.quantity || parseInt(operationForm.quantity) <= 0 || selectedZones.length === 0}
                          >
                            Distribuer automatiquement
                          </Button>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => setShowZoneSelection(true)}
                          >
                            Ajouter une zone
                          </Button>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          Capacité totale disponible: {getTotalAvailableCapacityAllZones(selectedZones)}
                        </div>
                      </div>
                      {parseInt(operationForm.quantity) > 0 && (
                        <div className={`p-3 rounded-lg ${
                          totalSelectedQuantity === parseInt(operationForm.quantity) 
                            ? 'bg-green-50 border border-green-200' 
                            : 'bg-red-50 border border-red-200'
                        }`}>
                          <p className={`text-sm font-medium ${
                            totalSelectedQuantity === parseInt(operationForm.quantity) 
                              ? 'text-green-800' 
                              : 'text-red-800'
                          }`}>
                            {totalSelectedQuantity === parseInt(operationForm.quantity) 
                              ? '✓ Quantité correspondante' 
                              : `⚠ Quantité manquante: ${parseInt(operationForm.quantity) - totalSelectedQuantity}`}
                          </p>
                          {totalSelectedQuantity < parseInt(operationForm.quantity) && (
                            <div className="text-xs text-red-600 mt-1">
                              <p>Capacité insuffisante dans cette zone.</p>
                              <p>Capacité totale disponible: {getTotalAvailableCapacityAllZones(selectedZones)}</p>
                              <p>Cliquez sur "Ajouter une zone" pour distribuer le reste.</p>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                  {showZoneSelection && (
                    <div className="border rounded-lg p-4 bg-gray-50">
                      <h4 className="font-medium mb-3">Sélectionner une nouvelle zone</h4>
                      <div className="grid grid-cols-2 gap-2">
                        {locations.filter(loc => !selectedZones.some(zone => zone.zone === loc)).map((location) => (
                          <Button
                            key={location}
                            variant="outline"
                            size="sm"
                            onClick={() => addNewZone(location)}
                            className="justify-start"
                          >
                            {location} ({getTotalAvailableCapacity(location)} places)
                          </Button>
                        ))}
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowZoneSelection(false)}
                        className="mt-2"
                      >
                        Annuler
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </>
          ) : (
            <>
              {/* Date de fabrication & expiration */}
              <div className="flex gap-4">
                <div className="flex-1">
                  <label className="block text-sm font-medium mb-1">Date de fabrication</label>
                  <Input
                    type="date"
                    value={operationForm.fabricationDate}
                    onChange={e => setOperationForm(f => ({ ...f, fabricationDate: e.target.value }))}
                    required
                  />
                </div>
                <div className="flex-1">
                  <label className="block text-sm font-medium mb-1">Date d'expiration (optionnelle)</label>
                  <Input
                    type="date"
                    value={operationForm.expirationDate}
                    onChange={e => setOperationForm(f => ({ ...f, expirationDate: e.target.value }))}
                  />
                </div>
              </div>
              {/* Product selection dropdown (moved under date inputs) */}
              <div className="mb-4">
                <label className="block text-sm font-medium mb-1">Produit</label>
                <Select
                  value={product?.id ? String(product.id) : ''}
                  onValueChange={val => {
                    const selected = finisProducts.find(p => String(p.id) === val);
                    setProduct(selected || null);
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner un produit fini" />
                  </SelectTrigger>
                  <SelectContent>
                   {/* Search input for product */}
                   {finisProducts.length > 0 && (
                     <div className="px-2 py-1">
                       <Input
                         type="text"
                         placeholder="Rechercher..."
                         value={productSearch}
                         onChange={e => setProductSearch(e.target.value)}
                         className="mb-2"
                         autoFocus
                       />
                     </div>
                   )}
                    {finisProducts
                     .filter(p => {
                       const search = productSearch.toLowerCase();
                       return (
                         (p.nom && p.nom.toLowerCase().includes(search)) ||
                         (p.reference && p.reference.toLowerCase().includes(search)) ||
                         String(p.id).includes(search)
                       );
                     })
                      .map(p => (
                        <SelectItem key={p.id} value={String(p.id)}>
                          {p.nom || p.reference || p.id}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>
              <Input
                type="number"
                placeholder="Quantité totale"
                value={operationForm.quantity}
                onChange={e => handleExitTotalQuantityChange(e.target.value)}
              />
              {/* Stock breakdown for Sortie: Entrée - Sortie per lot/location/etage/part */}
              {operationForm.quantity && parseInt(operationForm.quantity) > 0 && operationType === 'Sortie' && (
                <div className="mb-4">
                  <div className="space-y-3">
                    {(() => {
                      // Compute available stock per lot/location/etage/part: Entrée - Sortie
                      const entries = movementHistory.filter(m => m.status === 'Entrée');
                      const sorties = movementHistory.filter(m => m.status === 'Sortie');
                      type StockKey = string;
                      function makeKey(m: any) {
                        return [m.batch_number || m.lot || '', m.location_name || '', m.etage_name || '', m.part_id || ''].join('||');
                      }
                      // Sum entries
                      const stockMap: Record<StockKey, { lot: string, location: string, etage: string, part: string, entree: number, sortie: number, available: number }> = {};
                      for (const m of entries) {
                        const key = [m.batch_number || m.lot || '', m.location_name || '', m.etage_name || '', m.part_id || ''].join('||');
                        if (!stockMap[key]) {
                          stockMap[key] = {
                            lot: m.batch_number || m.lot || '',
                            location: m.location_name || '',
                            etage: m.etage_name || '',
                            part: m.part_name || '',
                            entree: 0,
                            sortie: 0,
                            available: 0,
                          };
                        }
                        stockMap[key].entree += m.quantity;
                        stockMap[key].available += m.quantity;
                      }
                      for (const m of sorties) {
                        const key = makeKey(m);
                        if (!stockMap[key]) {
                          stockMap[key] = {
                            lot: m.batch_number || m.lot || '',
                            location: m.location_name || '',
                            etage: m.etage_name || '',
                            part: m.part_name || '',
                            entree: 0,
                            sortie: 0,
                            available: 0,
                          };
                        }
                        stockMap[key].sortie += m.quantity;
                        stockMap[key].available -= m.quantity;
                      }
                      let entryList = Object.values(stockMap).filter(e => e.available > 0);
                      // Sort FIFO (by lot only, since entry_date is not present)
                      entryList.sort((a, b) => a.lot.localeCompare(b.lot));
                      // FIFO allocation for requested quantity
                      function fifoAllocateSortie(requestedQty: number, stockList: any[]) {
                        let remaining = requestedQty;
                        const allocation: Array<any> = [];
                        for (const stock of stockList) {
                          if (remaining <= 0) break;
                          const take = Math.min(stock.available, remaining);
                          allocation.push({ ...stock, taken: take });
                          remaining -= take;
                        }
                        return allocation;
                      }
                      const requestedQty = parseInt(operationForm.quantity) || 0;
                      const totalAvailable = entryList.reduce((sum, e) => sum + e.available, 0);
                      if (requestedQty > totalAvailable) {
                        return <div className="text-red-600 font-semibold">Stock insuffisant pour effectuer la sortie demandée.</div>;
                      }
                      const fifoRows = fifoAllocateSortie(requestedQty, entryList);
                      if (fifoRows.length === 0) {
                        return <p className="text-sm text-muted-foreground">Aucun stock disponible pour ce produit.</p>;
                      }
                      return (
                        <div>
                          <h4 className="font-medium mb-2">Stock utilisé pour cette sortie (FIFO)</h4>
                          <table className="min-w-full text-xs border mb-2">
                            <thead>
                              <tr>
                                <th className="border px-2 py-1">Lot</th>
                                <th className="border px-2 py-1">Emplacement</th>
                                <th className="border px-2 py-1">Étage</th>
                                <th className="border px-2 py-1">Partie</th>
                                <th className="border px-2 py-1">Disponible</th>
                                <th className="border px-2 py-1">Quantité prise</th>
                              </tr>
                            </thead>
                            <tbody>
                              {fifoRows.map((row, i) => (
                                <tr key={i}>
                                  <td className="border px-2 py-1">{row.lot}</td>
                                  <td className="border px-2 py-1">{row.location}</td>
                                  <td className="border px-2 py-1">{row.etage || '-'}</td>
                                  <td className="border px-2 py-1">{row.part || '-'}</td>
                                  <td className="border px-2 py-1">{row.available}</td>
                                  <td className="border px-2 py-1">{row.taken}</td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      );
                    })()}
                  </div>
                  <div className="mt-2 text-sm">
                    <span className="font-medium">Total demandé:</span> {operationForm.quantity}
                  </div>
                </div>
              )}
              {/* Remove zone selection UI for Sortie */}
              {/* Removed: {operationForm.location && ... zone selection UI ... } */}
              {operationForm.location && (
                <div className="space-y-4">
                  {selectedExitFloors.length > 1 && (
                    <div className="border rounded-lg p-4">
                      <h4 className="font-medium mb-3">Étages disponibles - {operationForm.location}</h4>
                      <div className="space-y-3">
                        {getAvailableFloors(operationForm.location).map((floor) => (
                          <div key={floor.id} className="flex items-center justify-between p-3 border rounded-lg">
                            <div className="flex items-center space-x-3">
                              <input
                                type="checkbox"
                                checked={selectedExitFloors.some(f => f.floorId === floor.id)}
                                onChange={() => handleExitFloorSelection(floor.id, floor.name, floor.availableCapacity)}
                                className="rounded"
                              />
                              <div>
                                <p className="font-medium">{floor.name}</p>
                                <p className="text-sm text-muted-foreground">
                                  Capacité disponible: {floor.availableCapacity} / {floor.totalCapacity}
                                </p>
                              </div>
                            </div>
                            {selectedExitFloors.some(f => f.floorId === floor.id) && (
                              <div className="flex items-center space-x-2">
                                <Input
                                  type="number"
                                  placeholder="Quantité"
                                  min="0"
                                  max={floor.availableCapacity}
                                  value={selectedExitFloors.find(f => f.floorId === floor.id)?.quantity || 0}
                                  onChange={(e) => handleExitFloorQuantityChange(floor.id, parseInt(e.target.value) || 0)}
                                  className="w-24"
                                />
                                <span className="text-sm text-muted-foreground"></span>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                      {parseInt(operationForm.quantity) > 0 && (
                        <div className={`p-3 rounded-lg ${
                          totalSelectedExitQuantity === parseInt(operationForm.quantity) 
                            ? 'bg-green-50 border border-green-200' 
                            : 'bg-red-50 border border-red-200'
                        }`}>
                          <p className={`text-sm font-medium ${
                            totalSelectedExitQuantity === parseInt(operationForm.quantity) 
                              ? 'text-green-800' 
                              : 'text-red-800'
                          }`}>
                            {totalSelectedExitQuantity === parseInt(operationForm.quantity) 
                              ? '✓ Quantité correspondante' 
                              : `⚠ Quantité manquante: ${parseInt(operationForm.quantity) - totalSelectedExitQuantity}`}
                          </p>
                          {totalSelectedExitQuantity < parseInt(operationForm.quantity) && (
                            <div className="text-xs text-red-600 mt-1">
                              <p>Capacité insuffisante dans cette zone.</p>
                              <p>Capacité totale disponible: {getTotalAvailableCapacityAllZones(selectedExitZones)}</p>
                              <p>Cliquez sur "Ajouter une zone" pour distribuer le reste.</p>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Annuler</Button>
          <Button
            disabled={
              operationType === 'Entrée'
                ? !operationForm.quantity || !operationForm.location || selectedFloors.length === 0 || totalSelectedQuantity !== parseInt(operationForm.quantity) || !operationForm.fabricationDate
                : !operationForm.quantity || (operationType === 'Sortie' && (() => {
                    // For Sortie, disable if requested quantity > available
                    const entries = movementHistory.filter(m => m.status === 'Entrée');
                    const sorties = movementHistory.filter(m => m.status === 'Sortie');
                    const totalAvailable = entries.reduce((sum, m) => sum + m.quantity, 0) - sorties.reduce((sum, m) => sum + m.quantity, 0);
                    return parseInt(operationForm.quantity) > totalAvailable;
                  })())
            }
            onClick={handleSave}
          >
            Enregistrer
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default FiniOperationClient; 