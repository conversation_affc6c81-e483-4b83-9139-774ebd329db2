import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom';
import Layout from '@/components/layout/Layout';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Search, ArrowLeft, Filter, X } from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogTrigger } from '@/components/ui/dialog';
import { toast } from 'sonner';
import OperationTypeDialog from '@/components/product/OperationTypeDialog';
import ProductOperationDialog from '@/components/product/ProductOperationDialog';
import { format } from 'date-fns';

// Helper to generate random mock data for product history
function getRandomInt(min: number, max: number) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

const STATUSES = ['Entrée', 'Sortie'];

function generateRandomHistoryEntry(index: number, productId: string) {
  const status = STATUSES[getRandomInt(0, 1)];
  const quantity = getRandomInt(1, 100);
  const location = ['Entrepôt A', 'Entrepôt B', 'Zone 1', 'Zone 2', 'Zone 3'][getRandomInt(0, 4)];
  const date = new Date();
  date.setDate(date.getDate() - getRandomInt(0, 30)); // Random date within last 30 days
  const hours = getRandomInt(8, 18);
  const minutes = getRandomInt(0, 59);
  
  return {
    id: `HIST-${productId}-${index}`,
    status,
    quantity,
    location,
    date: date.toISOString().split('T')[0],
    time: `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`,
    fournisseur: status === 'Entrée' ? ['Fournisseur A', 'Fournisseur B', 'Fournisseur C'][getRandomInt(0, 2)] : null,
    atelier: status === 'Sortie' ? ['Atelier 1', 'Atelier 2', 'Atelier 3'][getRandomInt(0, 2)] : null
  };
}

// Helper to convert data to CSV and trigger download
function exportToCSV(data: any[], filename: string) {
  if (!data.length) return;
  const headers = Object.keys(data[0]);
  const csvRows = [headers.join(',')];
  for (const row of data) {
    csvRows.push(headers.map(h => '"' + String(row[h] ?? '').replace(/"/g, '""') + '"').join(','));
  }
  const csvString = csvRows.join('\n');
  const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
  saveAs(blob, filename);
}

const ProductHistory = () => {
  const { productId } = useParams();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [product, setProduct] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    status: '',
    location: '',
    dateFrom: '',
    dateTo: '',
    fournisseur: '',
    atelier: ''
  });
  const [showFilters, setShowFilters] = useState(false);
  const [exportDialogOpen, setExportDialogOpen] = useState(false);
  const [exportDateFrom, setExportDateFrom] = useState(filters.dateFrom);
  const [exportDateTo, setExportDateTo] = useState(filters.dateTo);
  // Add state for operation dialog and form
  const [openOperation, setOpenOperation] = useState(false);
  const [operationType, setOperationType] = useState('');
  const [openForm, setOpenForm] = useState(false);
  const [operationForm, setOperationForm] = useState({ fournisseur: '', quantity: '', location: '' });
  const [locations, setLocations] = useState<string[]>([]);
  const [floorData, setFloorData] = useState<{ [key: string]: Array<{ id: number, name: string, availableCapacity: number, totalCapacity: number, type: 'etage' | 'part' }> }>({});
  const [movementHistory, setMovementHistory] = useState<any[]>([]);

  // Fetch real product data from backend
  useEffect(() => {
    if (!productId) return;
    fetch(`/api/products/${productId}`)
      .then(res => res.json())
      .then(data => {
        setProduct({
          ...data,
          alerte: data.alerte ?? data.alert ?? 0,
        });
        setLoading(false);
      })
      .catch(() => setLoading(false));
  }, [productId]);

  // Fetch movement history from backend
  useEffect(() => {
    if (!productId) return;
    fetch(`/api/movements?product_id=${productId}`)
      .then(res => res.json())
      .then(data => setMovementHistory(data))
      .catch(() => setMovementHistory([]));
  }, [productId]);

  // Fetch real locations and floor data
  useEffect(() => {
    fetch('/api/locations')
      .then(res => res.json())
      .then(locs => {
        setLocations(locs.map((l: any) => l.name));
        const data: { [key: string]: Array<{ id: number, name: string, availableCapacity: number, totalCapacity: number, type: 'etage' | 'part' }> } = {};
        locs.forEach((l: any) => {
          let items: Array<{ id: number, name: string, availableCapacity: number, totalCapacity: number, type: 'etage' | 'part' }>;
          if (l.etages && Array.isArray(l.etages) && l.etages.length > 0) {
            items = l.etages.map((item: any) => ({
              id: item.id,
              name: item.name,
              availableCapacity: item.currentStock !== undefined ? (item.places || 0) - item.currentStock : (item.places || 0),
              totalCapacity: item.places || 0,
              type: 'etage',
            }));
          } else if (l.parts && Array.isArray(l.parts) && l.parts.length > 0) {
            items = l.parts.map((item: any) => ({
              id: item.id,
              name: item.name,
              availableCapacity: item.currentStock !== undefined ? (item.maxCapacity || 0) - item.currentStock : (item.maxCapacity || 0),
              totalCapacity: item.maxCapacity || 0,
              type: 'part',
            }));
          } else {
            items = [];
          }
          data[l.name] = items;
        });
        setFloorData(data);
      });
  }, []);

  const filteredHistory = movementHistory.filter(entry => {
    // Search term filter
    const searchMatch = 
      (entry.status || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (entry.location_name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (entry.fournisseur_name && entry.fournisseur_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (entry.atelier && entry.atelier.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (entry.date || '').includes(searchTerm);

    // Status filter
    const statusMatch = !filters.status || entry.status === filters.status;

    // Location filter
    const locationMatch = !filters.location || entry.location_name === filters.location;

    // Date range filter
    const dateMatch = (!filters.dateFrom || entry.date >= filters.dateFrom) &&
                     (!filters.dateTo || entry.date <= filters.dateTo);

    // Fournisseur filter
    const fournisseurMatch = !filters.fournisseur || 
      (entry.fournisseur_name && entry.fournisseur_name === filters.fournisseur);

    // Atelier filter
    const atelierMatch = !filters.atelier || 
      (entry.atelier && entry.atelier === filters.atelier);

    return searchMatch && statusMatch && locationMatch && dateMatch && fournisseurMatch && atelierMatch;
  });

  // Helper to export to Excel (with summary rows)
  function exportToExcelWithSummary(data: any[], filename: string) {
    if (!data.length) return;
    // Prepare summary rows
    const summaryRows = [
      ["Nom", product?.nom ?? ""],
      ["Référence", product?.reference ?? ""],
      ["Unité", product?.unite ?? ""],
      ["Stock Actuel", product?.stock ?? ""],
      ["Total Entrées", product?.total_entrer ?? ""],
      ["Total Sorties", product?.total_sortie ?? ""],
      ["Seuil d'Alerte", product?.alerte ?? ""],
      [], // blank row
    ];
    // Prepare table data
    const wsTable = XLSX.utils.json_to_sheet(data);
    // Convert summaryRows to worksheet
    const wsSummary = XLSX.utils.aoa_to_sheet(summaryRows);
    // Merge summary and table: copy table rows after summary
    const tableRange = XLSX.utils.decode_range(wsTable['!ref'] || 'A1');
    for (let R = tableRange.s.r; R <= tableRange.e.r; ++R) {
      for (let C = tableRange.s.c; C <= tableRange.e.c; ++C) {
        const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });
        const targetAddress = XLSX.utils.encode_cell({ r: summaryRows.length + R, c: C });
        if (wsTable[cellAddress]) {
          wsSummary[targetAddress] = wsTable[cellAddress];
        }
      }
    }
    // Update the range
    wsSummary['!ref'] = XLSX.utils.encode_range({
      s: { r: 0, c: 0 },
      e: { r: summaryRows.length + (tableRange.e.r - tableRange.s.r), c: tableRange.e.c }
    });
    // Create workbook and export
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, wsSummary, 'Rapport');
    XLSX.writeFile(wb, filename);
  }

  // Filter for export (date range only)
  const exportFilteredHistory = movementHistory.filter(entry => {
    const dateMatch = (!exportDateFrom || entry.date >= exportDateFrom) && (!exportDateTo || entry.date <= exportDateTo);
    return dateMatch;
  });

  // Find the current place for 'Sortie' (last 'Entrée' location in history, or blank)
  const currentPlace = (() => {
    const lastEntry = movementHistory.slice().reverse().find(entry => entry.status === 'Entrée');
    return lastEntry ? lastEntry.location_name : '';
  })();

  // Helper to format date and time
  function formatDate(dateStr: string) {
    if (!dateStr) return '';
    return dateStr.slice(0, 10); // YYYY-MM-DD
  }
  function formatTime(timeStr: string) {
    if (!timeStr) return '';
    return timeStr.slice(0, 5); // HH:mm
  }

  // Extract unique fournisseurs and ateliers from movementHistory for filters
  const uniqueFournisseurs = Array.from(new Set(movementHistory.map(entry => entry.fournisseur_name).filter(Boolean)));
  const uniqueAteliers = Array.from(new Set(movementHistory.map(entry => entry.atelier).filter(Boolean)));

  if (loading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-[70vh]">
          <div className="w-16 h-16 border-4 border-black border-t-transparent rounded-full animate-spin"></div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="mb-4 sm:mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <Button
              variant="ghost"
              size="sm"
              className="mb-2"
              onClick={() => navigate('/products')}
            >
              <ArrowLeft size={16} className="mr-2" />
              Retour aux Matières
            </Button>
            <h1 className="text-2xl sm:text-3xl font-bold mb-1">Historique - {product?.nom}</h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Référence: {product?.reference} | Stock actuel: {product?.stock} {product?.unite}
            </p>
          </div>
          <div className="flex gap-2">
            <OperationTypeDialog
              open={openOperation}
              onOpenChange={setOpenOperation}
              onSelect={(type) => {
                setOperationType(type);
                setOpenOperation(false);
                setOpenForm(true);
              }}
              trigger={
                <Button variant="default" size="sm">
                  Nouvel opération
                </Button>
              }
            />
            <ProductOperationDialog
              open={openForm}
              onOpenChange={(open) => {
                setOpenForm(open);
                if (!open) setOperationType('');
              }}
              operationType={operationType as 'Entrée' | 'Sortie' | ''}
              onSuccess={() => {
                setOperationType('');
                setOpenForm(false);
              }}
              product={product}
              locations={locations}
              floorData={floorData}
              movementHistory={movementHistory} // <-- pass movementHistory here
            />
            {/* Exporter le rapport button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setExportDateFrom(filters.dateFrom);
                setExportDateTo(filters.dateTo);
                setExportDialogOpen(true);
              }}
            >
              Exporter le rapport
            </Button>
          </div>
        </div>
      </div>
      <Dialog open={exportDialogOpen} onOpenChange={setExportDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Exporter le rapport Excel</DialogTitle>
          </DialogHeader>
          <div className="grid grid-cols-2 gap-4 py-2">
            <div>
              <label className="text-sm font-medium">Date de début</label>
              <Input
                type="date"
                value={exportDateFrom}
                onChange={e => setExportDateFrom(e.target.value)}
              />
            </div>
            <div>
              <label className="text-sm font-medium">Date de fin</label>
              <Input
                type="date"
                value={exportDateTo}
                onChange={e => setExportDateTo(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              onClick={() => {
                exportToExcelWithSummary(exportFilteredHistory, `rapport-matiere-${product?.reference || ''}.xlsx`);
                setExportDialogOpen(false);
              }}
            >
              Exporter
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add the operation dialog at the end of the component */}
      {/* <Dialog open={openOperation} onOpenChange={setOpenOperation}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Nouvelle opération</DialogTitle>
          </DialogHeader>
          <div className="flex flex-col gap-4 py-4">
            <Button size="lg" variant="default" onClick={() => { setOperationType('Entrée'); setOpenOperation(false); setOpenForm(true); }}>Entrée</Button>
            <Button size="lg" variant="secondary" onClick={() => { setOperationType('Sortie'); setOpenOperation(false); setOpenForm(true); }}>Sortie</Button>
          </div>
        </DialogContent>
      </Dialog> */}

      {/* Add the operation form dialog at the end of the component */}
      {/* <Dialog open={openForm} onOpenChange={setOpenForm}>
        <DialogContent>
          <div className="flex items-center mb-2">
            <Button variant="ghost" size="icon" onClick={() => { setOpenForm(false); setOpenOperation(true); }}>
              <ArrowLeft size={18} />
            </Button>
            <DialogHeader className="flex-1">
              <DialogTitle>{operationType === 'Entrée' ? 'Nouvelle Entrée' : 'Nouvelle Sortie'}</DialogTitle>
            </DialogHeader>
          </div>
          <div className="space-y-4">
            {operationType === 'Entrée' ? (
              <Select value={operationForm.fournisseur} onValueChange={val => setOperationForm(f => ({ ...f, fournisseur: val }))}>
                <SelectTrigger><SelectValue placeholder="Sélectionner un fournisseur" /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="Fournisseur A">Fournisseur A</SelectItem>
                  <SelectItem value="Fournisseur B">Fournisseur B</SelectItem>
                  <SelectItem value="Fournisseur C">Fournisseur C</SelectItem>
                </SelectContent>
              </Select>
            ) : (
              <Input
                value={currentPlace}
                readOnly
                placeholder="Emplacement actuel"
              />
            )}
            <Input
              type="number"
              placeholder="Quantité"
              value={operationForm.quantity}
              onChange={e => setOperationForm(f => ({ ...f, quantity: e.target.value }))}
            />
            <Select value={operationForm.location} onValueChange={val => setOperationForm(f => ({ ...f, location: val }))}>
              <SelectTrigger>
                <SelectValue placeholder={operationType === 'Entrée' ? 'Emplacement' : 'Prochain emplacement'} />
              </SelectTrigger>
              <SelectContent>
                {LOCATIONS.map(loc => <SelectItem key={loc} value={loc}>{loc}</SelectItem>)}
              </SelectContent>
            </Select>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => { setOpenForm(false); setOperationType(''); setOperationForm({ fournisseur: '', quantity: '', location: '' }); }}>Annuler</Button>
            <Button
              disabled={
                operationType === 'Entrée'
                  ? !operationForm.fournisseur || !operationForm.quantity || !operationForm.location
                  : !operationForm.quantity || !operationForm.location
              }
              onClick={() => {
                setOpenForm(false);
                toast.success(
                  operationType === 'Entrée'
                    ? `${operationType} enregistrée: ${operationForm.quantity} à ${operationForm.location} (Fournisseur: ${operationForm.fournisseur})`
                    : `${operationType} enregistrée: ${operationForm.quantity} de ${currentPlace} vers ${operationForm.location}`
                );
                setOperationType('');
                setOperationForm({ fournisseur: '', quantity: '', location: '' });
              }}
            >
              Enregistrer
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog> */}

      {/* Product Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card>
          <div className="p-4">
            <p className="text-sm font-medium text-muted-foreground">Stock Actuel</p>
            <p className="text-2xl font-bold">{product?.stock ?? 0} {product?.unite ?? ''}</p>
          </div>
        </Card>

        <Card>
          <div className="p-4">
            <p className="text-sm font-medium text-muted-foreground">Total Entrées</p>
            <p className="text-2xl font-bold">{product?.total_entrer ?? 0} {product?.unite ?? ''}</p>
          </div>
        </Card>

        <Card>
          <div className="p-4">
            <p className="text-sm font-medium text-muted-foreground">Total Sorties</p>
            <p className="text-2xl font-bold">{product?.total_sortie ?? 0} {product?.unite ?? ''}</p>
          </div>
        </Card>

        <Card>
          <div className="p-4">
            <p className="text-sm font-medium text-muted-foreground">Seuil d'Alerte</p>
            <p className="text-2xl font-bold">{product?.alerte ?? 0} {product?.unite ?? ''}</p>
          </div>
        </Card>
      </div>

      <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Rechercher dans l'historique..."
            className="pl-10 text-sm sm:text-base"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        
        <div className="flex gap-2">
          <Popover open={showFilters} onOpenChange={setShowFilters}>
            <PopoverTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                <Filter className="h-4 w-4" />
                Filtres
                {Object.values(filters).some(filter => filter !== '') && (
                  <Badge variant="secondary" className="ml-1">
                    {Object.values(filters).filter(filter => filter !== '').length}
                  </Badge>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80" align="end">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Filtres</h4>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setFilters({
                      status: '',
                      location: '',
                      dateFrom: '',
                      dateTo: '',
                      fournisseur: '',
                      atelier: ''
                    })}
                    className="h-6 px-2"
                  >
                    <X className="h-3 w-3 mr-1" />
                    Effacer
                  </Button>
                </div>
                
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium">Statut</label>
                    <Select value={filters.status || "all"} onValueChange={(value) => setFilters(prev => ({ ...prev, status: value === "all" ? "" : value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Tous les statuts" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Tous les statuts</SelectItem>
                        <SelectItem value="Entrée">Entrée</SelectItem>
                        <SelectItem value="Sortie">Sortie</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Emplacement</label>
                    <Select value={filters.location || "all"} onValueChange={(value) => setFilters(prev => ({ ...prev, location: value === "all" ? "" : value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Tous les emplacements" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Tous les emplacements</SelectItem>
                        {locations.map((location) => (
                          <SelectItem key={location} value={location}>
                            {location}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <label className="text-sm font-medium">Date de début</label>
                      <Input
                        type="date"
                        value={filters.dateFrom}
                        onChange={(e) => setFilters(prev => ({ ...prev, dateFrom: e.target.value }))}
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Date de fin</label>
                      <Input
                        type="date"
                        value={filters.dateTo}
                        onChange={(e) => setFilters(prev => ({ ...prev, dateTo: e.target.value }))}
                      />
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Fournisseur</label>
                    <Select value={filters.fournisseur || "all"} onValueChange={(value) => setFilters(prev => ({ ...prev, fournisseur: value === "all" ? "" : value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Tous les fournisseurs" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Tous les fournisseurs</SelectItem>
                        {uniqueFournisseurs.map(f => (
                          <SelectItem key={f} value={f}>{f}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Atelier</label>
                    <Select value={filters.atelier || "all"} onValueChange={(value) => setFilters(prev => ({ ...prev, atelier: value === "all" ? "" : value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Tous les ateliers" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Tous les ateliers</SelectItem>
                        {uniqueAteliers.map(a => (
                          <SelectItem key={a} value={a}>{a}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>

      <Card>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Heure</TableHead>
                <TableHead>Statut</TableHead>
                <TableHead>Quantité</TableHead>
                <TableHead>Emplacement</TableHead>
                <TableHead>Étage/Place</TableHead>
                <TableHead>Fournisseur/Atelier</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredHistory.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    Aucun historique trouvé.
                  </TableCell>
                </TableRow>
              ) : (
                filteredHistory.map((entry) => (
                  <TableRow key={entry.id}>
                    <TableCell className="font-medium">{formatDate(entry.date)}</TableCell>
                    <TableCell>{formatTime(entry.time)}</TableCell>
                    <TableCell>
                      <Badge 
                        variant={entry.status === 'Entrée' ? 'default' : 'destructive'}
                        className={entry.status === 'Entrée' ? 'bg-green-600 hover:bg-green-700' : ''}
                      >
                        {entry.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="font-semibold">
                      {entry.quantity} {product?.unite}
                    </TableCell>
                    <TableCell>{entry.location_name}</TableCell>
                    <TableCell>{entry.etage_name || '-'}</TableCell>
                    <TableCell className="text-muted-foreground">
                      {entry.status === 'Entrée' ? entry.fournisseur_name : entry.atelier}
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </Card>
    </Layout>
  );
};

export default ProductHistory; 