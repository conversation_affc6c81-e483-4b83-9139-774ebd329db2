import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "@/hooks/use-theme";
import { AuthProvider } from "@/contexts/AuthContext";
import Index from "./pages/Index";
import Products from "./pages/Products";
import ProductDetail from "./pages/ProductDetail";
import Buyers from "./pages/Buyers";
import Settings from "./pages/Settings";
import Login from "./pages/Login";
import NotFound from "./pages/NotFound";
import Unauthorized from "./pages/Unauthorized";
import NewProduct from "./pages/NewProduct";
import Staff from "./pages/Staff";
import ProduitSemi from "./pages/ProduitSemi";
import ProduitFinis from "./pages/ProduitFinis";
import ProductHistory from "./pages/ProductHistory";
import FournisseurHistory from "./pages/FournisseurHistory";
import ProduitSemiDetail from "./pages/ProduitSemiDetail";
import ProduitFinisDetail from "./pages/ProduitFinisDetail";
import ProductMaterials from "./pages/ProductMaterials";
import Locations from "./pages/Locations";
import EditProduct from "./pages/EditProduct";
import ProduitClientDetail from "./pages/ProduitClientDetail";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <ThemeProvider defaultTheme="light">
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <Routes>
              <Route path="/login" element={<Login />} />
              <Route path="/unauthorized" element={<Unauthorized />} />
              
              {/* Protected Admin Routes */}
              <Route path="/" element={
                <Index />
              } />
              <Route path="/products" element={
                <Products />
              } />
              <Route path="/products/new" element={
                <NewProduct />
              } />
              <Route path="/products/edit/:id" element={
                <EditProduct />
              } />
           
              <Route path="/products/:productId" element={
                <ProductDetail />
              } />
              <Route path="/matiere/details/:productId" element={
                <ProductHistory />
              } />
           
      
              <Route path="/buyers" element={
                <Buyers />
              } />
              <Route path="/fournisseur" element={
                <Buyers />
              } />
            
         
             
              <Route path="/staff" element={
                <Staff />
              } />
              <Route path="/locations" element={
                <Locations />
              } />
              <Route path="/semi-products" element={
                <ProduitSemi />
              } />
              <Route path="/finished-products" element={
                <ProduitFinis />
              } />
              <Route path="/semi-products/details/:id" element={
                <ProduitSemiDetail />
              } />
              <Route path="/produitfinis/:reference" element={
                <ProduitFinisDetail />
              } />
              <Route path="/settings" element={
                <Settings />
              } />
              <Route path="/fournisseur/history/:fournisseurId" element={
                <FournisseurHistory />
              } />
              <Route path="/product-materials" element={
                <ProductMaterials />
              } />
              <Route path="/ready" element={<ProduitClientDetail />} />
              
              {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </BrowserRouter>
        </TooltipProvider>
      </ThemeProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
