import React, { useState, useEffect } from "react";
import Layout from '@/components/layout/Layout';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { MoreHorizontal, Edit, Trash2, Plus, Eye } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import type { Zone, ZoneWithEtages, ZoneWithParts, Part, ZoneType } from "../types";

// Extend Etage locally to allow optional currentStock for display
type EtageWithStock = { name: string; places: number; currentStock?: number };

// Remove MOCK_LOCATIONS and replace with MOCK_ZONES
// const MOCK_ZONES: Zone[] = [
//   {
//     id: "1",
//     type: "with_etages",
//     name: "Zone A",
//     description: "Zone avec étages",
//     etages: [
//       { name: "Étage 1", places: 20 },
//       { name: "Étage 2", places: 30 },
//     ],
//   },
//   {
//     id: "2",
//     type: "with_parts",
//     name: "Zone B",
//     description: "Zone divisée en parties",
//     parts: [
//       { name: "Partie 1", maxCapacity: 40, currentStock: 10 },
//       { name: "Partie 2", maxCapacity: 60, currentStock: 30 },
//     ],
//   },
// ];

// Form state types
const emptyForm = {
  type: "with_etages" as ZoneType,
  name: "",
  description: "",
  etages: [{ name: "Étage 1", places: 0, currentStock: 0 }],
  parts: [{ name: "Partie 1", maxCapacity: 0, currentStock: 0 }],
};

type FormState = typeof emptyForm;

const Locations: React.FC = () => {
  const [zones, setZones] = useState<Zone[]>([]);
  const [openMenuIndex, setOpenMenuIndex] = useState<number | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editIndex, setEditIndex] = useState<number | null>(null);
  const [form, setForm] = useState<FormState>(emptyForm);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [detailsZone, setDetailsZone] = useState<Zone | null>(null);
  const [search, setSearch] = useState("");
  const [detailsSearch, setDetailsSearch] = useState("");

  useEffect(() => {
    fetch('/api/locations')
      .then(res => res.json())
      .then(data => setZones(data))
      .catch(err => console.error('Failed to fetch locations', err));
  }, []);

  // Handle zone type change
  const handleTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const type = e.target.value as ZoneType;
    setForm((prev) => ({
      ...prev,
      type,
      etages: type === "with_etages" ? [{ name: "Étage 1", places: 0, currentStock: 0 }] : [],
      parts: type === "with_parts" ? [{ name: "Partie 1", maxCapacity: 0, currentStock: 0 }] : [],
    }));
  };

  // Open dialog for add or edit
  const openAddDialog = () => {
    setForm(emptyForm);
    setEditIndex(null);
    setDialogOpen(true);
  };
  const openEditDialog = (idx: number) => {
    const zone = zones[idx];
    if (zone.type === "with_etages") {
      const etagesWithNames = zone.etages.length
        ? zone.etages.map((et, i) => ({
            ...et,
            name: et.name && et.name.trim() !== "" ? et.name : `Étage ${i + 1}`,
            currentStock: et.currentStock || 0,
          }))
        : [{ name: "Étage 1", places: 0, currentStock: 0 }];
      setForm({
        type: zone.type,
        name: zone.name,
        description: zone.description || "",
        etages: etagesWithNames,
        parts: [],
      });
    } else {
      const partsWithNames = zone.parts.length
        ? zone.parts.map((pt, i) => ({
            ...pt,
            name: pt.name && pt.name.trim() !== "" ? pt.name : `Partie ${i + 1}`,
            currentStock: pt.currentStock || 0,
          }))
        : [{ name: "Partie 1", maxCapacity: 0, currentStock: 0 }];
      setForm({
        type: zone.type,
        name: zone.name,
        description: zone.description || "",
        etages: [],
        parts: partsWithNames,
      });
    }
    setEditIndex(idx);
    setDialogOpen(true);
    setOpenMenuIndex(null);
  };

  // Handle form changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setForm((prev) => ({ ...prev, [name]: value }));
  };

  // Handle dynamic etages/parts changes
  const handleEtageChange = (i: number, field: keyof EtageWithStock, value: string | number) => {
    setForm((prev) => ({
      ...prev,
      etages: prev.etages.map((et, idx) => {
        if (idx !== i) return et;
        if (field === 'currentStock') {
          const max = et.places || 0;
          const val = Math.max(0, Math.min(Number(value), max));
          return { ...et, currentStock: val };
        }
        if (field === 'places') {
          // If places is reduced below currentStock, also reduce currentStock
          const newPlaces = Number(value);
          const newCurrentStock = Math.min(et.currentStock || 0, newPlaces);
          return { ...et, places: newPlaces, currentStock: newCurrentStock };
        }
        return { ...et, [field]: value };
      }),
    }));
  };

  const handlePartChange = (i: number, field: keyof Part, value: string | number) => {
    setForm((prev) => ({
      ...prev,
      parts: prev.parts.map((pt, idx) => {
        if (idx !== i) return pt;
        if (field === 'currentStock') {
          const max = pt.maxCapacity || 0;
          const val = Math.max(0, Math.min(Number(value), max));
          return { ...pt, currentStock: val };
        }
        if (field === 'maxCapacity') {
          // If maxCapacity is reduced below currentStock, also reduce currentStock
          const newMax = Number(value);
          const newCurrentStock = Math.min(pt.currentStock || 0, newMax);
          return { ...pt, maxCapacity: newMax, currentStock: newCurrentStock };
        }
        return { ...pt, [field]: value };
      }),
    }));
  };

  // Add/remove etage/part
  const addEtage = () => setForm((prev) => ({
    ...prev,
    etages: [
      ...prev.etages,
      { name: `Étage ${prev.etages.length + 1}`, places: 0, currentStock: 0 }
    ]
  }));
  const removeEtage = (i: number) => setForm((prev) => ({ ...prev, etages: prev.etages.filter((_, idx) => idx !== i) }));
  const addPart = () => setForm((prev) => ({
    ...prev,
    parts: [
      ...prev.parts,
      { name: `Partie ${prev.parts.length + 1}`, maxCapacity: 0, currentStock: 0 }
    ]
  }));
  const removePart = (i: number) => setForm((prev) => ({ ...prev, parts: prev.parts.filter((_, idx) => idx !== i) }));

  // Save zone (add or edit)
  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!form.name) return;
    if (form.type === "with_etages" && form.etages.length === 0) return;
    if (form.type === "with_parts" && form.parts.length === 0) return;

    // Prepare payload
    const payload = {
      name: form.name,
      description: form.description,
      type: form.type,
      etages: form.type === "with_etages" ? form.etages : undefined,
      parts: form.type === "with_parts" ? form.parts : undefined,
    };

    let res;
    if (editIndex !== null) {
      // EDIT
      const zone = zones[editIndex];
      res = await fetch(`/api/locations/${zone.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
    } else {
      // ADD
      res = await fetch('/api/locations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
    }

    if (res.ok) {
      const newZone = await res.json();
      if (editIndex !== null) {
        setZones(prev => prev.map((z, i) => (i === editIndex ? newZone : z)));
      } else {
        setZones(prev => [...prev, newZone]);
      }
      setDialogOpen(false);
      setForm(emptyForm);
      setEditIndex(null);
    } else {
      const data = await res.json();
      if (data?.error === "DUPLICATE_NAME" || data?.details?.code === "ER_DUP_ENTRY") {
        alert("Une zone avec ce nom existe déjà. Veuillez choisir un autre nom.");
      } else {
        alert("Erreur lors de l'ajout ou modification de la zone. Veuillez réessayer.");
      }
    }
  };

  // Delete zone from backend and update UI
  const handleDelete = async (idx: number) => {
    const zone = zones[idx];
    if (!zone) return;
    if (!window.confirm(`Supprimer la zone: ${zone.name} ? Cette action est irréversible.`)) {
      setOpenMenuIndex(null);
      return;
    }
    try {
      const res = await fetch(`/api/locations/${zone.id}`, { method: 'DELETE' });
      if (res.ok) {
      setZones(zones.filter((_, i) => i !== idx));
      } else {
        const data = await res.json();
        alert(data.error || 'Erreur lors de la suppression de la zone');
      }
    } catch (err) {
      alert('Erreur réseau lors de la suppression de la zone');
    }
    setOpenMenuIndex(null);
  };

  // Show details popup
  const openDetails = (zone: Zone) => {
    setDetailsZone(zone);
    setDetailsOpen(true);
    setOpenMenuIndex(null);
  };

  // Filtered zones based on search
  const filteredZones = zones.filter(
    z =>
      z.name.toLowerCase().includes(search.toLowerCase()) ||
      (z.description || "").toLowerCase().includes(search.toLowerCase())
  );

  // Move the details dialog rendering logic to a function to avoid JSX IIFE
  function renderDetailsZone() {
    if (!detailsZone) return null;
            // Compute overall stats
            let totalMax = 0, totalUsed = 0;
            if (detailsZone.type === 'with_etages') {
              totalMax = detailsZone.etages.reduce((sum, et) => sum + (et.places || 0), 0);
      totalUsed = detailsZone.etages.reduce((sum, et) => sum + (et.currentStock || 0), 0);
            } else {
              totalMax = detailsZone.parts.reduce((sum, pt) => sum + (pt.maxCapacity || 0), 0);
              totalUsed = detailsZone.parts.reduce((sum, pt) => sum + (pt.currentStock || 0), 0);
            }
            const totalRest = totalMax - totalUsed;
            const isFull = totalRest <= 0;

            // Search bar for details
            let filteredRows = [];
            if (detailsZone.type === 'with_etages') {
              filteredRows = (detailsZone.etages as EtageWithStock[]).filter(et => et.name.toLowerCase().includes(detailsSearch.toLowerCase()));
            } else {
              filteredRows = detailsZone.parts.filter(pt => pt.name.toLowerCase().includes(detailsSearch.toLowerCase()));
            }

            return (
              <div className="space-y-6">
                {/* General Zone Details */}
                <div className="border-b pb-4 mb-2">
                  <div className="text-lg font-semibold mb-2">Informations générales</div>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div><b>Nom:</b> {detailsZone.name}</div>
                    <div><b>Type:</b> {detailsZone.type === 'with_etages' ? 'Étages' : 'Parties'}</div>
                    <div><b>Description:</b> {detailsZone.description || <span className="text-muted-foreground">-</span>}</div>
                    <div><b>Capacité totale:</b> {totalMax}</div>
                    <div><b>Occupé:</b> {totalUsed}</div>
                    <div><b>Restant:</b> {totalRest}</div>
                    <div><b>Statut global:</b> <span className={`inline-block px-2 py-1 rounded text-xs font-semibold ${isFull ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'}`}>{isFull ? 'Plein' : 'Disponible'}</span></div>
                  </div>
                </div>
                {/* Search bar for details */}
                <div className="mb-2">
                  <Input
                    type="text"
                    placeholder={`Rechercher par nom ${detailsZone.type === 'with_etages' ? 'd\'étage' : 'de partie'}...`}
                    value={detailsSearch}
                    onChange={e => setDetailsSearch(e.target.value)}
                    className="w-full"
                  />
                </div>
                {/* Table of Etages or Parts */}
                <div>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Nom</TableHead>
                        <TableHead>Capacité max</TableHead>
                        <TableHead>Places restantes</TableHead>
                        <TableHead>Statut</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {detailsZone.type === 'with_etages'
                        ? filteredRows.map((et: EtageWithStock, i) => {
                            const max = et.places || 0;
                            const used = et.currentStock || 0;
                            const rest = max - used;
                            const isFull = rest <= 0;
                            return (
                              <TableRow key={i}>
                                <TableCell>{et.name}</TableCell>
                                <TableCell>{max}</TableCell>
                                <TableCell>{rest}</TableCell>
                                <TableCell>
                                  <span className={`inline-block px-2 py-1 rounded text-xs font-semibold ${isFull ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'}`}>
                                    {isFull ? 'Plein' : 'Disponible'}
                                  </span>
                                </TableCell>
                              </TableRow>
                            );
                          })
                        : filteredRows.map((pt, i) => {
                            const max = pt.maxCapacity || 0;
                            const used = pt.currentStock || 0;
                            const rest = max - used;
                            const isFull = rest <= 0;
                            return (
                              <TableRow key={i}>
                                <TableCell>{pt.name}</TableCell>
                                <TableCell>{max}</TableCell>
                                <TableCell>{rest}</TableCell>
                                <TableCell>
                                  <span className={`inline-block px-2 py-1 rounded text-xs font-semibold ${isFull ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'}`}>
                                    {isFull ? 'Plein' : 'Disponible'}
                                  </span>
                                </TableCell>
                              </TableRow>
                            );
                          })}
                    </TableBody>
                  </Table>
                </div>
              </div>
            );
  }

  return (
    <Layout>
      <div className="mb-4 sm:mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold mb-1">Zones d'entrepôt</h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              Liste et gestion des zones de stockage dans votre entrepôt.
            </p>
          </div>
          <Button onClick={openAddDialog}>
            <Plus className="h-4 w-4 mr-2" />
            Ajouter une zone
          </Button>
        </div>
      </div>
      {/* Search bar */}
      <div className="mb-4">
        <Input
          type="text"
          placeholder="Rechercher une zone..."
          value={search}
          onChange={e => setSearch(e.target.value)}
          className="w-full"
        />
      </div>
      <Card className="overflow-hidden">
        <div className="overflow-x-auto" style={{ maxWidth: '100vw' }}>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Nom</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Capacité max</TableHead>
                <TableHead>Places restantes</TableHead>
                <TableHead>Statut</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredZones.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    Aucune zone trouvée.
                  </TableCell>
                </TableRow>
              ) : (
                filteredZones.map((zone, idx) => {
                  let maxCapacity = 0;
                  let used = 0;
                  if (zone.type === 'with_etages') {
                    maxCapacity = zone.etages.reduce((sum, et) => sum + (et.places || 0), 0);
                    used = zone.etages.reduce((sum, et) => sum + (et.currentStock || 0), 0);
                  } else {
                    maxCapacity = zone.parts.reduce((sum, pt) => sum + (pt.maxCapacity || 0), 0);
                    used = zone.parts.reduce((sum, pt) => sum + (pt.currentStock || 0), 0);
                  }
                  const remaining = maxCapacity - used;
                  const isFull = remaining <= 0;
                  return (
                    <TableRow key={zone.id}>
                      <TableCell>{zone.name}</TableCell>
                      <TableCell>{zone.type === 'with_etages' ? 'Étages' : 'Parties'}</TableCell>
                      <TableCell>{zone.description}</TableCell>
                      <TableCell>{maxCapacity}</TableCell>
                      <TableCell>{remaining > 0 ? remaining : 0}</TableCell>
                      <TableCell>
                        <span className={`inline-block px-2 py-1 rounded text-xs font-semibold ${isFull ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'}`}>
                          {isFull ? 'Plein' : 'Disponible'}
                        </span>
                      </TableCell>
                      <TableCell className="text-right flex gap-2 justify-end">
                        <Button variant="outline" size="sm" onClick={() => openDetails(zone)}>
                          <Eye className="h-4 w-4 mr-1" />
                        </Button>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" onClick={() => setOpenMenuIndex(openMenuIndex === idx ? null : idx)}>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          {openMenuIndex === idx && (
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem className="gap-2" onClick={() => openEditDialog(idx)}>
                                <Edit className="h-4 w-4 text-blue-600" />
                                Modifier
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem className="gap-2 text-red-600" onClick={() => handleDelete(idx)}>
                                <Trash2 className="h-4 w-4" />
                                Supprimer
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          )}
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </div>
      </Card>
      {/* Details Dialog */}
      <Dialog open={detailsOpen} onOpenChange={setDetailsOpen}>
        <DialogContent className="max-w-3xl w-full custom-scrollbar" style={{ maxHeight: '80vh', overflowY: 'auto' }}>
          <DialogHeader>
            <DialogTitle>Détails de la zone</DialogTitle>
          </DialogHeader>
          {detailsZone && renderDetailsZone()}
        </DialogContent>
      </Dialog>
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="max-w-2xl w-full custom-scrollbar" style={{ maxHeight: '80vh', overflowY: 'auto' }}>
          <DialogHeader>
            <DialogTitle>{editIndex !== null ? "Modifier la zone" : "Ajouter une zone"}</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSave} className="space-y-4">
            <div>
              <Label htmlFor="type">Type de zone</Label>
              <select id="type" name="type" value={form.type} onChange={handleTypeChange} className="w-full border rounded px-2 py-1">
                <option value="with_etages">Avec étages</option>
                <option value="with_parts">Divisée en parties</option>
              </select>
            </div>
            <div>
              <Label htmlFor="name">Nom</Label>
              <Input id="name" name="name" value={form.name} onChange={handleChange} required />
            </div>
            <div>
              <Label htmlFor="description">Description</Label>
              <Input id="description" name="description" value={form.description} onChange={handleChange} />
            </div>
            {form.type === 'with_etages' && (
              <div>
                <Label>Étages</Label>
                <div className="flex gap-2 mb-1 text-xs font-semibold text-muted-foreground">
                  <div style={{ flex: 1, minWidth: 0 }}>Nom de l'étage</div>
                  <div style={{ width: 96, minWidth: 96, textAlign: 'center' }}>Places</div>
                  <div style={{ width: 128, minWidth: 128, textAlign: 'center' }}>Stock actuel</div>
                  <div style={{ width: 32, minWidth: 32, textAlign: 'center' }}></div>
                </div>
                {(form.etages as EtageWithStock[]).map((et, i) => (
                  <div key={i} className="flex gap-2 items-center mb-2">
                    <Input
                      value={et.name}
                      readOnly
                      style={{ flex: 1, minWidth: 0, background: '#f3f4f6' }}
                    />
                    <Input
                      type="number"
                      min={1}
                      placeholder="Places"
                      value={et.places}
                      onChange={e => handleEtageChange(i, 'places', e.target.value)}
                      style={{ width: 96, minWidth: 96, textAlign: 'center' }}
                      required
                    />
                    <Input
                      type="number"
                      min={0}
                      placeholder="Stock actuel"
                      value={et.currentStock || 0}
                      onChange={e => handleEtageChange(i, 'currentStock', e.target.value)}
                      style={{ width: 128, minWidth: 128, textAlign: 'center' }}
                      required
                    />
                    <Button
                      type="button"
                      variant="destructive"
                      size="icon"
                      onClick={() => removeEtage(i)}
                      disabled={form.etages.length === 1}
                      style={{ width: 32, minWidth: 32 }}
                    >
                      -
                    </Button>
                  </div>
                ))}
                <Button type="button" variant="outline" onClick={addEtage}>Ajouter un étage</Button>
              </div>
            )}
            {form.type === 'with_parts' && (
              <div>
                <Label>Parties</Label>
                <div className="flex gap-2 mb-1 text-xs font-semibold text-muted-foreground">
                  <div style={{ flex: 1, minWidth: 0 }}>Nom de la partie</div>
                  <div style={{ width: 128, minWidth: 128, textAlign: 'center' }}>Capacité max</div>
                  <div style={{ width: 128, minWidth: 128, textAlign: 'center' }}>Stock actuel</div>
                  <div style={{ width: 32, minWidth: 32, textAlign: 'center' }}></div>
                </div>
                {form.parts.map((pt, i) => (
                  <div key={i} className="flex gap-2 items-center mb-2">
                    <Input
                      value={pt.name}
                      readOnly
                      style={{ flex: 1, minWidth: 0, background: '#f3f4f6' }}
                    />
                    <Input
                      type="number"
                      min={1}
                      placeholder="Capacité max"
                      value={pt.maxCapacity}
                      onChange={e => handlePartChange(i, 'maxCapacity', e.target.value)}
                      style={{ width: 128, minWidth: 128, textAlign: 'center' }}
                      required
                    />
                    <Input
                      type="number"
                      min={0}
                      placeholder="Stock actuel"
                      value={pt.currentStock}
                      onChange={e => handlePartChange(i, 'currentStock', e.target.value)}
                      style={{ width: 128, minWidth: 128, textAlign: 'center' }}
                      required
                    />
                    <Button
                      type="button"
                      variant="destructive"
                      size="icon"
                      onClick={() => removePart(i)}
                      disabled={form.parts.length === 1}
                      style={{ width: 32, minWidth: 32 }}
                    >
                      -
                    </Button>
                  </div>
                ))}
                <Button type="button" variant="outline" onClick={addPart}>Ajouter une partie</Button>
              </div>
            )}
            <DialogFooter>
              <Button type="submit">{editIndex !== null ? "Enregistrer" : "Ajouter"}</Button>
              <DialogClose asChild>
                <Button type="button" variant="outline" onClick={() => setDialogOpen(false)}>
                  Annuler
                </Button>
              </DialogClose>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
      <style>{`
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  background: transparent;
}
.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(100, 100, 100, 0.25);
  border-radius: 4px;
}
.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(100, 100, 100, 0.4);
}
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(100,100,100,0.25) transparent;
}
`}</style>
    </Layout>
  );
};

export default Locations; 